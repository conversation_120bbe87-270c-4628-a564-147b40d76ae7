# Database Fixes and Permissions Guide

## Overview

This document consolidates all the database fixes and permission configurations needed for the ONKO SaaS platform to function correctly in production. It includes the essential SQL scripts and explanations for setting up proper Row Level Security (RLS) policies and permissions.

## Table of Contents

1. [Database Setup](#database-setup)
2. [Auth Schema Permissions](#auth-schema-permissions)
3. [Products and Variants RLS Policies](#products-and-variants-rls-policies)
4. [Stock History RLS Policies](#stock-history-rls-policies)
5. [Stock Adjustment Triggers with Error Handling](#stock-adjustment-triggers-with-error-handling)
6. [Verification Queries](#verification-queries)

## Database Setup

The main database setup is defined in [supabase-setup.sql](file://x:\Qoder\onko\supabase-setup.sql). This file contains the complete schema definition for all tables, functions, and initial RLS policies.

## Auth Schema Permissions

To ensure proper functioning of the application, the following permissions must be granted on the auth schema:

```sql
-- Ensure that the auth schema is accessible
-- This is needed for referencing auth.users in foreign keys
GRANT USAGE ON SCHEMA auth TO authenticated;
GRANT SELECT ON auth.users TO authenticated;

-- Create a proper policy for accessing user information
-- This allows authenticated users to read their own user record
CREATE POLICY "Users can read own user record" ON auth.users
  FOR SELECT USING (id = auth.uid());
```

## Products and Variants RLS Policies

Proper RLS policies must be enabled on the products and product_variants tables:

```sql
-- Ensure RLS is enabled on products table
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Create policies for products table
DROP POLICY IF EXISTS "Users can view own products" ON products;
CREATE POLICY "Users can view own products" ON products
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can create own products" ON products;
CREATE POLICY "Users can create own products" ON products
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update own products" ON products;
CREATE POLICY "Users can update own products" ON products
  FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete own products" ON products;
CREATE POLICY "Users can delete own products" ON products
  FOR DELETE USING (auth.uid() = user_id);

-- Ensure RLS is enabled on product_variants table
ALTER TABLE product_variants ENABLE ROW LEVEL SECURITY;

-- Create policies for product_variants table
DROP POLICY IF EXISTS "Users can view own product variants" ON product_variants;
CREATE POLICY "Users can view own product variants" ON product_variants
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can create own product variants" ON product_variants;
CREATE POLICY "Users can create own product variants" ON product_variants
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update own product variants" ON product_variants;
CREATE POLICY "Users can update own product variants" ON product_variants
  FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete own product variants" ON product_variants;
CREATE POLICY "Users can delete own product variants" ON product_variants
  FOR DELETE USING (auth.uid() = user_id);

-- Grant necessary permissions
GRANT ALL ON products TO authenticated;
GRANT ALL ON product_variants TO authenticated;
```

## Stock History RLS Policies

The stock_history table also requires proper RLS policies:

```sql
-- Ensure RLS is enabled on stock_history table
ALTER TABLE stock_history ENABLE ROW LEVEL SECURITY;

-- Create policies for stock_history table
DROP POLICY IF EXISTS "Users can view own stock history" ON stock_history;
CREATE POLICY "Users can view own stock history" ON stock_history
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can create own stock history" ON stock_history;
CREATE POLICY "Users can create own stock history" ON stock_history
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Grant necessary permissions
GRANT ALL ON stock_history TO authenticated;
```

## Stock Adjustment Triggers with Error Handling

The stock adjustment triggers need to handle permission errors gracefully:

```sql
-- Function to create a notification for stock adjustments
CREATE OR REPLACE FUNCTION create_stock_adjustment_notification()
RETURNS TRIGGER AS $$
DECLARE
  product_name TEXT;
  product_sku TEXT;
  user_email TEXT;
  notification_title TEXT;
  notification_message TEXT;
BEGIN
  -- Only proceed if stock quantity actually changed
  IF OLD.stock_quantity IS DISTINCT FROM NEW.stock_quantity THEN
    -- Get product information
    SELECT p.name, p.base_sku INTO product_name, product_sku
    FROM products p
    WHERE p.id = NEW.id;
    
    -- Get user email for display name (with error handling)
    BEGIN
      SELECT email INTO user_email
      FROM auth.users
      WHERE id = NEW.user_id;
    EXCEPTION
      WHEN insufficient_privilege THEN
        user_email := 'user';
    END;
    
    -- Create notification title and message
    notification_title := 'Stock Adjustment: ' || COALESCE(product_name, 'Unknown Product');
    notification_message := 'Stock for "' || COALESCE(product_name, 'Unknown Product') || 
                           '" (SKU: ' || COALESCE(product_sku, 'N/A') || 
                           ') was adjusted from ' || COALESCE(OLD.stock_quantity, 0) || 
                           ' to ' || COALESCE(NEW.stock_quantity, 0) || '.';
    
    -- Insert notification
    INSERT INTO notifications (
      user_id,
      type,
      title,
      message,
      related_entity_type,
      related_entity_id,
      created_at
    ) VALUES (
      NEW.user_id,
      'stock_adjustment',
      notification_title,
      notification_message,
      'product',
      NEW.id,
      NOW()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to create a notification for variant stock adjustments
CREATE OR REPLACE FUNCTION create_variant_stock_adjustment_notification()
RETURNS TRIGGER AS $$
DECLARE
  product_name TEXT;
  variant_name TEXT;
  variant_sku TEXT;
  user_email TEXT;
  notification_title TEXT;
  notification_message TEXT;
BEGIN
  -- Only proceed if stock quantity actually changed
  IF OLD.stock_quantity IS DISTINCT FROM NEW.stock_quantity THEN
    -- Get product and variant information
    SELECT p.name, pv.variant_name, pv.sku INTO product_name, variant_name, variant_sku
    FROM product_variants pv
    JOIN products p ON pv.product_id = p.id
    WHERE pv.id = NEW.id;
    
    -- Get user email for display name (with error handling)
    BEGIN
      SELECT email INTO user_email
      FROM auth.users
      WHERE id = NEW.user_id;
    EXCEPTION
      WHEN insufficient_privilege THEN
        user_email := 'user';
    END;
    
    -- Create notification title and message
    notification_title := 'Stock Adjustment: ' || COALESCE(product_name, 'Unknown Product') || 
                         ' - ' || COALESCE(variant_name, 'Unknown Variant');
    notification_message := 'Stock for "' || COALESCE(product_name, 'Unknown Product') || 
                           ' - ' || COALESCE(variant_name, 'Unknown Variant') || 
                           '" (SKU: ' || COALESCE(variant_sku, 'N/A') || 
                           ') was adjusted from ' || COALESCE(OLD.stock_quantity, 0) || 
                           ' to ' || COALESCE(NEW.stock_quantity, 0) || '.';
    
    -- Insert notification
    INSERT INTO notifications (
      user_id,
      type,
      title,
      message,
      related_entity_type,
      related_entity_id,
      created_at
    ) VALUES (
      NEW.user_id,
      'stock_adjustment',
      notification_title,
      notification_message,
      'variant',
      NEW.id,
      NOW()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## Verification Queries

Use these queries to verify that the permissions and policies are correctly set up:

```sql
-- Check if RLS is enabled on tables
SELECT tablename, rowsecurity FROM pg_tables WHERE tablename IN ('products', 'product_variants', 'stock_history') AND schemaname = 'public';

-- List policies on products table
SELECT polname, polrelid::regclass, polcmd, polqual, polwithcheck
FROM pg_policy
WHERE polrelid = 'products'::regclass;

-- List policies on product_variants table
SELECT polname, polrelid::regclass, polcmd, polqual, polwithcheck
FROM pg_policy
WHERE polrelid = 'product_variants'::regclass;

-- List policies on stock_history table
SELECT polname, polrelid::regclass, polcmd, polqual, polwithcheck
FROM pg_policy
WHERE polrelid = 'stock_history'::regclass;

-- Check if authenticated role has necessary permissions
SELECT grantee, table_name, privilege_type
FROM information_schema.role_table_grants
WHERE table_schema = 'public' AND table_name IN ('products', 'product_variants', 'stock_history') AND grantee = 'authenticated';

-- Check auth schema permissions
SELECT grantee, table_name, privilege_type
FROM information_schema.role_table_grants
WHERE table_schema = 'auth' AND table_name = 'users' AND grantee = 'authenticated';
```

## Deployment Instructions

1. Run the main database setup script [supabase-setup.sql](file://x:\Qoder\onko\supabase-setup.sql) in your Supabase SQL Editor
2. Apply the fixes and permissions from this document
3. Wait 10-15 minutes for the schema cache to refresh
4. Restart your development server
5. Test the stock adjustment functionality

## Troubleshooting

If you encounter permission errors:
1. Verify that all the policies and permissions in this document have been applied
2. Check that the auth schema permissions are correctly set
3. Ensure that RLS is enabled on all relevant tables
4. Wait for the schema cache to refresh (10-15 minutes)
5. Restart your development server

This consolidated guide replaces all previous fix documentation and scripts, providing a single source of truth for database permissions and fixes.