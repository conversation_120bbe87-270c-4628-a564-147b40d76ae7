# Supabase Storage Configuration

## Overview
This document describes how storage is set up in the ONKO application, particularly for storing user profile pictures.

## Storage Buckets
The application uses the following storage buckets:

### 1. Avatars Bucket
- **Name**: `avatars`
- **Purpose**: Stores user profile pictures
- **Access**: Public bucket with controlled access via Row Level Security (RLS)
- **File structure**: Files are stored in user-specific folders using the pattern `{user_id}/avatar.{extension}`
- **File size limit**: 5MB
- **Allowed file types**: Image files (JPEG, PNG, GIF, WebP)

## Setup
The storage configuration is set up using the script `scripts/setup-avatars-bucket.js`. This script:
- Creates the avatars bucket if it doesn't exist
- Updates bucket settings (public access, file size limits, allowed MIME types)

To run the setup script:
```bash
node scripts/setup-avatars-bucket.js
```

Additionally, you need to set up the storage policies through the Supabase dashboard UI as described in `STORAGE_SETUP_INSTRUCTIONS.md`.

## Implementation in the Application
The profile picture upload is implemented in `components/profile/profile-picture-manager.tsx`. The component:

1. Allows users to upload an image file
2. Validates the file type and size
3. Uploads the file to Supabase Storage in the avatars bucket
4. Gets the public URL for the uploaded file
5. Updates the user's profile with the new avatar URL

## Troubleshooting

### "Bucket not found" Error
If you encounter a "Bucket not found" error when uploading profile pictures:

1. Verify that the avatars bucket exists in your Supabase project
   - Go to the Supabase dashboard > Storage > Buckets
   - Check if the "avatars" bucket exists

2. If it doesn't exist, run the setup script:
   ```bash
   node scripts/setup-avatars-bucket.js
   ```

3. Alternatively, manually create the bucket:
   - Go to the Supabase dashboard > Storage > Buckets
   - Click "New Bucket"
   - Name: "avatars"
   - Public bucket: Yes
   - Set file size limit to 5MB
   - Allow MIME types: image/jpeg, image/png, image/gif, image/webp

### "new row violates row-level security policy" Error
This error occurs when the storage policies are not properly configured. In Supabase, storage policies need to be set up manually through the dashboard UI:

1. Follow the instructions in `STORAGE_SETUP_INSTRUCTIONS.md` to set up the policies through the Supabase dashboard
2. The policies control who can upload, download, update, and delete files in the avatars bucket

### Other Storage Issues
For other storage-related issues:

1. Check the browser console for specific error messages
2. Verify your Supabase credentials in .env.local
3. Check if the user is properly authenticated
4. Inspect the storage policies in the Supabase dashboard