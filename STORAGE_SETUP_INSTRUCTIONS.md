# Storage Setup Instructions

## Overview
This document provides step-by-step instructions to properly configure Supabase Storage for the ONKO application, specifically for profile picture uploads.

## Prerequisites
Before setting up storage policies, ensure you have:
1. A Supabase project
2. The avatars bucket created (run `node scripts/setup-avatars-bucket.js` or create manually)

## Step 1: Run the Avatars Bucket Setup Script
First, run the script to ensure the avatars bucket exists with correct settings:

```bash
node scripts/setup-avatars-bucket.js
```

If this fails or you prefer manual setup, follow the manual instructions in STORAGE_CONFIGURATION.md.

## Step 2: Set Up Storage Policies Manually (Required)

### Using Supabase Dashboard UI (Recommended)
Storage policies in Supabase are best managed through the dashboard UI:

1. Go to your Supabase dashboard
2. Select your project
3. Navigate to Storage > Buckets
4. Find the "avatars" bucket (create it if it doesn't exist with settings below)
5. Click on the "Policies" tab for the avatars bucket
6. Create the following policies:

#### Insert (Upload) Policy
- **Name**: Authenticated users can upload avatars
- **Operation**: INSERT
- **Roles**: authenticated
- **Policy Definition**: `bucket_id = 'avatars' AND auth.role() = 'authenticated'`

#### Select (Download) Policy
- **Name**: Anyone can view avatars
- **Operation**: SELECT
- **Roles**: public
- **Policy Definition**: `bucket_id = 'avatars'`

#### Update Policy
- **Name**: Users can update their own avatars
- **Operation**: UPDATE
- **Roles**: authenticated
- **Policy Definition**: `bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]`

#### Delete Policy
- **Name**: Users can delete their own avatars
- **Operation**: DELETE
- **Roles**: authenticated
- **Policy Definition**: `bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]`

### Manual Bucket Creation (if needed)
If the avatars bucket doesn't exist, create it manually:
1. Go to Storage > Buckets
2. Click "New Bucket"
3. Set the following properties:
   - **Name**: avatars
   - **Public**: Yes (checked)
   - **File size limit**: 5MB
   - **Allowed MIME types**: 
     - image/jpeg
     - image/png
     - image/gif
     - image/webp

## Verification
After setting up the policies:

1. Try uploading a profile picture in the application
2. Check the browser console for any errors
3. Verify the file appears in the avatars bucket in your Supabase dashboard

## Troubleshooting
If you continue to experience issues:

1. Double-check that all four policies have been created correctly
2. Ensure the avatars bucket exists and has the correct settings:
   - Public: Yes
   - File size limit: 5MB
   - Allowed MIME types: image/jpeg, image/png, image/gif, image/webp
3. Check that your Supabase environment variables are correctly set in `.env.local`
4. Verify that users are properly authenticated when uploading files

## Additional Notes
- The file structure in the avatars bucket should be `{user_id}/avatar.{extension}`
- Users can only access their own avatar files due to the RLS policies
- Public read access allows anyone to view avatar images (needed for displaying profile pictures)
- If you encounter permission errors when running SQL scripts, it's because storage policies are typically managed through the dashboard UI rather than SQL commands