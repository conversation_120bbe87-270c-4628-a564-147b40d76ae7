import { describe, it, expect } from '@jest/globals';

// Mock the formatCurrency function since it's not relevant for this test
jest.mock('@/lib/currency', () => ({
  formatCurrency: (value: number) => `$${value.toFixed(2)}`,
}));

// Mock the Supabase client
const mockSupabase = {
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  single: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
};

jest.mock('@/lib/supabase', () => ({
  getSupabaseClient: () => mockSupabase,
}));

// Mock React hooks and context
jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useState: jest.fn(),
  useEffect: jest.fn(),
}));

jest.mock('@/contexts/auth-context', () => ({
  useAuth: () => ({ user: { id: 'test-user-id' } }),
}));

jest.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({ toast: jest.fn() }),
}));

// Mock window.dispatchEvent
Object.defineProperty(window, 'dispatchEvent', {
  writable: true,
  value: jest.fn(),
});

describe('Unit Cost Calculation', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should calculate pure unit cost for variants without packaging cost', () => {
    // Mock variant data with base_cost and packaging_cost
    const mockVariants = [
      {
        id: 'variant-1',
        base_cost: 10.00,
        packaging_cost: 2.00,
        price: 25.00,
        stock_quantity: 100,
        low_stock_threshold: 10,
        size: 'M',
        color: 'Red',
        sku: 'PROD-001-M-RED',
        variant_name: 'Size: M, Color: Red',
      }
    ];

    // For a variant with base_cost: 10.00 and packaging_cost: 2.00
    // The total cost should be 12.00
    const variant = mockVariants[0];
    const totalCost = (variant.base_cost || 0) + (variant.packaging_cost || 0);
    
    expect(totalCost).toBe(12.00);
    expect(variant.base_cost).toBe(10.00);
    expect(variant.packaging_cost).toBe(2.00);
  });

  it('should handle variants with null cost values', () => {
    const variant = {
      id: 'variant-2',
      base_cost: null,
      packaging_cost: null,
      cost_adjustment: 15.00, // Legacy field
      price: 30.00,
      stock_quantity: 50,
    };

    // If base_cost and packaging_cost are null, we should fall back to cost_adjustment
    const unitCost = variant.base_cost !== undefined && variant.base_cost !== null 
      ? variant.base_cost 
      : (variant.cost_adjustment || 0);

    expect(unitCost).toBe(15.00);
  });
});