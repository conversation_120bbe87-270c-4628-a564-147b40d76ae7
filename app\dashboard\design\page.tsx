'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { TabNavigation, TabConfig } from '@/components/ui/tab-navigation'
import { Badge } from '@/components/ui/badge'
import { 
  Package, 
  DollarSign, 
  ShoppingCart, 
  TrendingUp,
  Plus, 
  Search, 
  Filter, 
  Download, 
  Upload, 
  Edit, 
  Edit3, 
  Trash2, 
  Eye, 
  MoreHorizontal,
  Calendar,
  CheckCircle,
  AlertCircle,
  Info,
  XCircle,
  MoreVertical,
  ChevronDown,
  ChevronRight,
  ChevronsUpDown,
  Command,
  Tag,
  Tags,
  SlidersHorizontal,
  RotateCcw,
  X,
  Save,
  Check
} from 'lucide-react'
import { useCurrency } from '@/lib/currency'
import { DatePicker } from '@/components/ui/date-picker'
import { CustomDropdown, type CustomDropdownOption } from '@/components/ui/custom-dropdown'

// Mock data for examples
const mockExpenses = [
  { id: '1', expense_id: 'EXP-001', amount: 125.50, description: 'Office supplies', category: 'Office', vendor: 'Staples', expense_date: '2023-06-15', created_at: '2023-06-15T10:30:00Z' },
  { id: '2', expense_id: 'EXP-002', amount: 89.99, description: 'Software subscription', category: 'Software', vendor: 'Adobe', expense_date: '2023-06-10', created_at: '2023-06-10T14:20:00Z' },
  { id: '3', expense_id: 'EXP-003', amount: 250.00, description: 'Business lunch', category: 'Meals', vendor: 'Restaurant XYZ', expense_date: '2023-06-05', created_at: '2023-06-05T12:45:00Z' },
]

const mockMetrics = {
  totalProducts: 42,
  monthlyRevenue: 12500,
  lowStockItems: 5,
  totalExpenses: 3250,
  expenseCount: 28
}

const DEFAULT_CATEGORIES: CustomDropdownOption[] = [
  { value: 'Office', label: 'Office Supplies' },
  { value: 'Travel', label: 'Travel & Transportation' },
  { value: 'Meals', label: 'Meals & Entertainment' },
  { value: 'Software', label: 'Software & Subscriptions' },
  { value: 'Marketing', label: 'Marketing & Advertising' },
]

// Container component with title and divider line (matching settings page design)
function Container({ title, children }: { title: string; children: React.ReactNode }) {
  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="px-4 py-2 border-b border-gray-200">
        <h3 className="text-xs font-semibold text-black">{title}</h3>
      </div>
      <div className="p-3">
        {children}
      </div>
    </div>
  )
}

export default function DesignPage() {
  const { formatCurrency } = useCurrency()
  const [expenseDate, setExpenseDate] = useState<Date | undefined>(new Date())
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [amount, setAmount] = useState('')
  const [description, setDescription] = useState('')
  const [vendor, setVendor] = useState('')
  
  // Define tabs configuration
  const tabs: TabConfig[] = [
    {
      id: 'components',
      label: 'Components',
      content: (
        <div className="space-y-4">
          <Container title="Card Design Options">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
                    <CardTitle className="text-sm font-medium text-gray-800">Total Products</CardTitle>
                    <div className="h-6 w-6 rounded-md bg-blue-50 flex items-center justify-center">
                      <Package className="h-3 w-3 text-blue-600" />
                    </div>
                  </CardHeader>
                  <CardContent className="p-6 pt-0">
                    <div className="text-2xl font-bold text-gray-900">{mockMetrics.totalProducts}</div>
                    <p className="text-xs text-gray-500">Products in inventory</p>
                  </CardContent>
                </Card>
                
                <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
                    <CardTitle className="text-sm font-medium text-gray-800">Monthly Revenue</CardTitle>
                    <div className="h-6 w-6 rounded-md bg-blue-50 flex items-center justify-center">
                      <DollarSign className="h-3 w-3 text-blue-600" />
                    </div>
                  </CardHeader>
                  <CardContent className="p-6 pt-0">
                    <div className="text-2xl font-bold text-gray-900">{formatCurrency(mockMetrics.monthlyRevenue)}</div>
                    <p className="text-xs text-gray-500">Revenue this month</p>
                  </CardContent>
                </Card>
                
                <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
                    <CardTitle className="text-sm font-medium text-gray-800">Low Stock Items</CardTitle>
                    <div className="h-6 w-6 rounded-md bg-blue-50 flex items-center justify-center">
                      <AlertCircle className="h-3 w-3 text-blue-600" />
                    </div>
                  </CardHeader>
                  <CardContent className="p-6 pt-0">
                    <div className="text-2xl font-bold text-gray-900">{mockMetrics.lowStockItems}</div>
                    <p className="text-xs text-gray-500">Items need reordering</p>
                  </CardContent>
                </Card>
                
                <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
                    <CardTitle className="text-sm font-medium text-gray-800">Total Expenses</CardTitle>
                    <div className="h-6 w-6 rounded-md bg-blue-50 flex items-center justify-center">
                      <ShoppingCart className="h-3 w-3 text-blue-600" />
                    </div>
                  </CardHeader>
                  <CardContent className="p-6 pt-0">
                    <div className="text-2xl font-bold text-gray-900">{formatCurrency(mockMetrics.totalExpenses)}</div>
                    <p className="text-xs text-gray-500">Expenses this month</p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </Container>

          {/* Form Components */}
          <Container title="Form Components">
            <div className="grid gap-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="space-y-2">
                  <Label htmlFor="expense-date" className="text-xs">Expense Date</Label>
                  <DatePicker 
                    date={expenseDate} 
                    onDateChange={setExpenseDate} 
                    className="h-8 text-xs placeholder:text-xs"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="amount" className="text-xs">Amount</Label>
                  <div className="relative">
                    <Input
                      id="amount"
                      type="number"
                      placeholder="0.00"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      className="h-8 text-xs placeholder:text-xs"
                    />
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description" className="text-xs">Description</Label>
                <Input
                  id="description"
                  placeholder="Enter expense description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="h-8 text-xs placeholder:text-xs"
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="space-y-2">
                  <Label htmlFor="vendor" className="text-xs">Vendor</Label>
                  <Input
                    id="vendor"
                    placeholder="Enter vendor name"
                    value={vendor}
                    onChange={(e) => setVendor(e.target.value)}
                    className="h-8 text-xs placeholder:text-xs"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="category" className="text-xs">Category</Label>
                  <CustomDropdown
                    options={DEFAULT_CATEGORIES}
                    value={selectedCategory}
                    onValueChange={setSelectedCategory}
                    placeholder="Select category"
                    className="h-8 text-xs placeholder:text-xs"
                    onAddCustomOption={(newOption) => {
                      console.log('New category added:', newOption)
                    }}
                  />
                </div>
              </div>
              
              <div className="flex justify-end pt-1">
                <Button size="sm" className="h-8 text-xs">
                  <Plus className="mr-1 h-3 w-3" />
                  Record Expense
                </Button>
              </div>
            </div>
          </Container>

          {/* Table Components */}
          <Container title="Table Components">
            <div className="rounded-md border">
              <div className="overflow-x-auto">
                <table className="w-full text-xs">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">
                        <div className="flex items-center">
                          <span>ID</span>
                        </div>
                      </th>
                      <th className="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">
                        <div className="flex items-center">
                          <span>Description</span>
                        </div>
                      </th>
                      <th className="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">
                        <div className="flex items-center">
                          <span>Category</span>
                        </div>
                      </th>
                      <th className="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">
                        <div className="flex items-center">
                          <span>Amount</span>
                        </div>
                      </th>
                      <th className="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">
                        <div className="flex items-center">
                          <span>Date</span>
                        </div>
                      </th>
                      <th className="px-3 py-2 text-left font-medium text-gray-500 uppercase tracking-wider">
                        <div className="flex items-center">
                          <span>Actions</span>
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {mockExpenses.map((expense) => (
                      <tr key={expense.id} className="hover:bg-gray-50">
                        <td className="px-3 py-2 whitespace-nowrap font-medium text-gray-900">
                          {expense.expense_id}
                        </td>
                        <td className="px-3 py-2">
                          <div className="font-medium text-gray-900">{expense.description}</div>
                          <div className="text-gray-500">{expense.vendor}</div>
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap">
                          <Badge variant="secondary" className="text-xs px-1.5 py-0.5">{expense.category}</Badge>
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap font-medium text-gray-900">
                          {formatCurrency(expense.amount)}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap text-gray-500">
                          {new Date(expense.expense_date).toLocaleDateString()}
                        </td>
                        <td className="px-3 py-2 whitespace-nowrap">
                          <div className="flex items-center space-x-1">
                            <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                              <Eye className="h-3 w-3" />
                            </Button>
                            <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </Container>
        </div>
      )
    },
    {
      id: 'typography',
      label: 'Typography',
      content: (
        <div className="space-y-4">
          <Container title="Typography">
            <div className="space-y-4">
              <div>
                <h1 className="text-xl font-bold tracking-tight">Heading 1 (text-xl font-bold)</h1>
                <p className="text-xs text-gray-500 mt-1">Used for main page titles</p>
              </div>
              
              <div>
                <h2 className="text-lg font-bold tracking-tight">Heading 2 (text-lg font-bold)</h2>
                <p className="text-xs text-gray-500 mt-1">Used for section titles</p>
              </div>
              
              <div>
                <h3 className="text-base font-semibold tracking-tight">Heading 3 (text-base font-semibold)</h3>
                <p className="text-xs text-gray-500 mt-1">Used for card titles</p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium">Heading 4 (text-sm font-medium)</h4>
                <p className="text-xs text-gray-500 mt-1">Used for subsection titles</p>
              </div>
              
              <div>
                <p className="text-sm">Base text (text-sm) - Default body text</p>
                <p className="text-xs text-gray-500 mt-1">Used for most body content</p>
              </div>
              
              <div>
                <p className="text-xs">Small text (text-xs) - Supporting text and descriptions</p>
                <p className="text-[10px] text-gray-500 mt-1">Used for labels, captions, and secondary information</p>
              </div>
            </div>
          </Container>
          
          <Container title="Font Weights">
            <div className="space-y-2">
              <p className="font-thin text-sm">Thin (font-thin)</p>
              <p className="font-extralight text-sm">Extra Light (font-extralight)</p>
              <p className="font-light text-sm">Light (font-light)</p>
              <p className="font-normal text-sm">Normal (font-normal)</p>
              <p className="font-medium text-sm">Medium (font-medium)</p>
              <p className="font-semibold text-sm">Semibold (font-semibold)</p>
              <p className="font-bold text-sm">Bold (font-bold)</p>
              <p className="font-extrabold text-sm">Extra Bold (font-extrabold)</p>
              <p className="font-black text-sm">Black (font-black)</p>
            </div>
          </Container>
        </div>
      )
    },
    {
      id: 'colors',
      label: 'Colors',
      content: (
        <div className="space-y-4">
          <Container title="Color Palette">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <h3 className="text-xs font-medium">Primary Colors</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded bg-blue-600"></div>
                    <div>
                      <p className="text-xs font-medium">Primary Blue</p>
                      <p className="text-[10px] text-gray-500">bg-blue-600</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded bg-blue-500"></div>
                    <div>
                      <p className="text-xs font-medium">Primary Hover</p>
                      <p className="text-[10px] text-gray-500">bg-blue-500</p>
                    </div>
                  </div>
                </div>
                
                <h3 className="text-xs font-medium mt-3">Background Colors</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded border bg-white"></div>
                    <div>
                      <p className="text-xs font-medium">White</p>
                      <p className="text-[10px] text-gray-500">bg-white</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded bg-gray-50"></div>
                    <div>
                      <p className="text-xs font-medium">Light Gray</p>
                      <p className="text-[10px] text-gray-500">bg-gray-50</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded bg-gray-100"></div>
                    <div>
                      <p className="text-xs font-medium">Gray</p>
                      <p className="text-[10px] text-gray-500">bg-gray-100</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-3">
                <h3 className="text-xs font-medium">Text Colors</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded flex items-center justify-center bg-gray-900 text-white text-xs">Aa</div>
                    <div>
                      <p className="text-xs font-medium">Primary Text</p>
                      <p className="text-[10px] text-gray-500">text-gray-900</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded flex items-center justify-center bg-gray-700 text-white text-xs">Aa</div>
                    <div>
                      <p className="text-xs font-medium">Secondary Text</p>
                      <p className="text-[10px] text-gray-500">text-gray-700</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded flex items-center justify-center bg-gray-500 text-white text-xs">Aa</div>
                    <div>
                      <p className="text-xs font-medium">Muted Text</p>
                      <p className="text-[10px] text-gray-500">text-gray-500</p>
                    </div>
                  </div>
                </div>
                
                <h3 className="text-xs font-medium mt-3">Status Colors</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded bg-green-500"></div>
                    <div>
                      <p className="text-xs font-medium">Success</p>
                      <p className="text-[10px] text-gray-500">bg-green-500</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded bg-yellow-500"></div>
                    <div>
                      <p className="text-xs font-medium">Warning</p>
                      <p className="text-[10px] text-gray-500">bg-yellow-500</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded bg-red-500"></div>
                    <div>
                      <p className="text-xs font-medium">Error</p>
                      <p className="text-[10px] text-gray-500">bg-red-500</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>
      )
    },
    {
      id: 'layouts',
      label: 'Layouts',
      content: (
        <div className="space-y-4">
          <Container title="Layout Patterns">
            <div className="space-y-4">
              <div>
                <h3 className="text-xs font-medium mb-2">Dashboard Grid Layout</h3>
                <div className="border rounded p-3">
                  <div className="grid gap-3 md:grid-cols-3">
                    <div className="h-20 bg-gray-100 rounded flex items-center justify-center">
                      <span className="text-xs text-gray-500">Metric Card</span>
                    </div>
                    <div className="h-20 bg-gray-100 rounded flex items-center justify-center">
                      <span className="text-xs text-gray-500">Metric Card</span>
                    </div>
                    <div className="h-20 bg-gray-100 rounded flex items-center justify-center">
                      <span className="text-xs text-gray-500">Metric Card</span>
                    </div>
                  </div>
                  <div className="grid gap-3 mt-3 md:grid-cols-2">
                    <div className="h-48 bg-gray-100 rounded flex items-center justify-center">
                      <span className="text-xs text-gray-500">Main Content Area</span>
                    </div>
                    <div className="h-48 bg-gray-100 rounded flex items-center justify-center">
                      <span className="text-xs text-gray-500">Secondary Content</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-xs font-medium mb-2">List Layout with Actions</h3>
                <div className="border rounded p-3 space-y-2">
                  <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div>
                      <p className="font-medium text-sm">Item Title</p>
                      <p className="text-xs text-gray-500">Item description</p>
                    </div>
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div>
                      <p className="font-medium text-sm">Item Title</p>
                      <p className="text-xs text-gray-500">Item description</p>
                    </div>
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-xs font-medium mb-2">Spacing Guidelines</h3>
                <div className="border rounded p-3">
                  <div className="space-y-3">
                    <div>
                      <p className="text-xs font-medium mb-1">Container Padding: p-3 (12px) / p-4 (16px)</p>
                      <div className="flex gap-3">
                        <div className="p-3 bg-gray-100 rounded">
                          <div className="w-12 h-12 bg-white rounded"></div>
                        </div>
                        <div className="p-4 bg-gray-100 rounded">
                          <div className="w-12 h-12 bg-white rounded"></div>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <p className="text-xs font-medium mb-1">Grid Gap: gap-3 (12px) / gap-4 (16px)</p>
                      <div className="flex gap-3">
                        <div className="w-12 h-12 bg-gray-100 rounded"></div>
                        <div className="w-12 h-12 bg-gray-100 rounded"></div>
                        <div className="w-12 h-12 bg-gray-100 rounded"></div>
                      </div>
                    </div>
                    
                    <div>
                      <p className="text-xs font-medium mb-1">Vertical Spacing: space-y-3 (12px) / space-y-4 (16px)</p>
                      <div className="space-y-3">
                        <div className="h-8 bg-gray-100 rounded"></div>
                        <div className="h-8 bg-gray-100 rounded"></div>
                        <div className="h-8 bg-gray-100 rounded"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Container>
        </div>
      )
    },
    {
      id: 'filter-ui',
      label: 'Filter UI',
      content: (
        <div className="space-y-4">
          {/* Two-Tier Filter System */}
          <Container title="Filter Component (Finalized Design)">
            <div className="space-y-3">
              {/* Top tier: Always visible filters */}
              <div className="flex flex-col sm:flex-row gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-500" />
                  <Input className="pl-8 h-8 text-xs" placeholder="Search expenses..." />
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" className="h-8 text-xs whitespace-nowrap">
                    <Calendar className="h-3.5 w-3.5 mr-1.5" />
                    This Month
                  </Button>
                  <Button variant="outline" size="sm" className="h-8 text-xs relative">
                    <SlidersHorizontal className="h-3.5 w-3.5" />
                    <span className="absolute -top-1 -right-1 flex items-center justify-center w-4 h-4 rounded-full bg-red-500 text-white text-[10px] font-medium">3</span>
                  </Button>
                </div>
              </div>
              
              {/* Active filters indicator */}
              <div className="flex flex-wrap gap-1.5">
                <Badge className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
                  <span>Category: Office Supplies</span>
                  <X className="h-3 w-3 cursor-pointer" />
                </Badge>
                <Badge className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
                  <span>Amount: > $100</span>
                  <X className="h-3 w-3 cursor-pointer" />
                </Badge>
                <Badge className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
                  <span>Status: Pending</span>
                  <X className="h-3 w-3 cursor-pointer" />
                </Badge>
              </div>
              
              {/* Second tier: Expandable advanced filters */}
              <div className="border border-gray-200 rounded-lg p-4 bg-white">
                <div className="text-xs font-medium mb-3 pb-2 border-b border-gray-100">Advanced Filters</div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                  {/* Category filter */}
                  <div className="space-y-1">
                    <Label className="text-xs font-medium">Categories</Label>
                    <Select defaultValue="office">
                      <SelectTrigger className="h-8 text-xs">
                        <SelectValue placeholder="Select categories" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="office">Office Supplies</SelectItem>
                        <SelectItem value="software">Software</SelectItem>
                        <SelectItem value="travel">Travel</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Payment Method filter */}
                  <div className="space-y-1">
                    <Label className="text-xs font-medium">Payment Method</Label>
                    <Select defaultValue="card">
                      <SelectTrigger className="h-8 text-xs">
                        <SelectValue placeholder="Select method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="card">Credit Card</SelectItem>
                        <SelectItem value="cash">Cash</SelectItem>
                        <SelectItem value="bank">Bank Transfer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Amount Range filter */}
                  <div className="space-y-1">
                    <Label className="text-xs font-medium">Amount Range</Label>
                    <div className="flex gap-2 items-center">
                      <Input className="h-8 text-xs" placeholder="Min" defaultValue="100" />
                      <span className="text-xs text-gray-500">to</span>
                      <Input className="h-8 text-xs" placeholder="Max" />
                    </div>
                  </div>
                  
                  {/* Status filter */}
                  <div className="space-y-1">
                    <Label className="text-xs font-medium">Status</Label>
                    <Select defaultValue="pending">
                      <SelectTrigger className="h-8 text-xs">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="processed">Processed</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Vendor filter */}
                  <div className="space-y-1">
                    <Label className="text-xs font-medium">Vendor</Label>
                    <Input className="h-8 text-xs" placeholder="Search vendors" />
                  </div>
                  
                  {/* Date Range filter */}
                  <div className="space-y-1">
                    <Label className="text-xs font-medium">Specific Date Range</Label>
                    <div className="flex gap-2 items-center">
                      <DatePicker date={new Date()} onDateChange={() => {}} className="h-8 text-xs flex-1" placeholder="From" />
                      <span className="text-xs text-gray-500">to</span>
                      <DatePicker date={undefined} onDateChange={() => {}} className="h-8 text-xs flex-1" placeholder="To" />
                    </div>
                  </div>
                  
                  {/* Tax Deductible filter */}
                  <div className="space-y-1 flex items-center">
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="tax-deductible" className="h-4 w-4 rounded border-gray-300" />
                      <Label htmlFor="tax-deductible" className="text-xs font-medium">Tax Deductible Only</Label>
                    </div>
                  </div>
                  
                  {/* Has Receipt filter */}
                  <div className="space-y-1 flex items-center">
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="has-receipt" className="h-4 w-4 rounded border-gray-300" />
                      <Label htmlFor="has-receipt" className="text-xs font-medium">Has Receipt Only</Label>
                    </div>
                  </div>
                </div>
                
                {/* Common predefined filters - REMOVED */}
                
                {/* Action buttons */}
                <div className="mt-4 pt-3 border-t border-gray-100 flex justify-end gap-2">
                  <Button variant="outline" size="sm" className="h-7 text-xs">
                    <RotateCcw className="h-3 w-3 mr-1.5" />
                    Reset
                  </Button>
                  <Button size="sm" className="h-7 text-xs">
                    Apply Filters
                  </Button>
                </div>
              </div>
              
              {/* Sample results after filters */}
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-gray-50 py-2 px-3 border-b border-gray-200 flex justify-between items-center">
                  <div className="text-xs font-medium">Filtered Results</div>
                  <div className="text-xs text-gray-500">Showing 3 of 45 expenses</div>
                </div>
                <table className="w-full text-xs">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="px-3 py-2 text-left font-medium text-gray-500">Date</th>
                      <th className="px-3 py-2 text-left font-medium text-gray-500">Description</th>
                      <th className="px-3 py-2 text-left font-medium text-gray-500">Category</th>
                      <th className="px-3 py-2 text-left font-medium text-gray-500">Amount</th>
                      <th className="px-3 py-2 text-left font-medium text-gray-500">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-gray-100">
                      <td className="px-3 py-2">May 15, 2023</td>
                      <td className="px-3 py-2">Office chair</td>
                      <td className="px-3 py-2">
                        <Badge variant="secondary" className="text-xs px-1.5 py-0.5">Office Supplies</Badge>
                      </td>
                      <td className="px-3 py-2 font-medium">$149.99</td>
                      <td className="px-3 py-2">
                        <Badge className="bg-yellow-50 text-yellow-700 border-none text-xs px-1.5 py-0.5">Pending</Badge>
                      </td>
                    </tr>
                    <tr className="border-b border-gray-100">
                      <td className="px-3 py-2">May 12, 2023</td>
                      <td className="px-3 py-2">Adobe subscription</td>
                      <td className="px-3 py-2">
                        <Badge variant="secondary" className="text-xs px-1.5 py-0.5">Software</Badge>
                      </td>
                      <td className="px-3 py-2 font-medium">$52.99</td>
                      <td className="px-3 py-2">
                        <Badge className="bg-yellow-50 text-yellow-700 border-none text-xs px-1.5 py-0.5">Pending</Badge>
                      </td>
                    </tr>
                    <tr>
                      <td className="px-3 py-2">May 10, 2023</td>
                      <td className="px-3 py-2">Client lunch</td>
                      <td className="px-3 py-2">
                        <Badge variant="secondary" className="text-xs px-1.5 py-0.5">Meals</Badge>
                      </td>
                      <td className="px-3 py-2 font-medium">$125.00</td>
                      <td className="px-3 py-2">
                        <Badge className="bg-yellow-50 text-yellow-700 border-none text-xs px-1.5 py-0.5">Pending</Badge>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              <strong>Pros:</strong> Common filters always visible, progressive disclosure, organized, scalable<br />
              <strong>Cons:</strong> Advanced filters take up more space when expanded, but only when needed
            </div>
          </Container>
        </div>
      )
    },
    {
      "id": "table-ui",
      "label": "Table UI",
      "content": (
        <div className="space-y-6">
          {/* Filter Component (Finalized Design) - Reused from Filter UI tab */}
          <Container title="Filter Component (Finalized Design)">
            <div className="space-y-3">
              {/* Top tier: Always visible filters */}
              <div className="flex flex-col sm:flex-row gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-500" />
                  <Input className="pl-8 h-8 text-xs" placeholder="Search expenses..." />
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" className="h-8 text-xs whitespace-nowrap">
                    <Calendar className="h-3.5 w-3.5 mr-1.5" />
                    This Month
                  </Button>
                  <Button variant="outline" size="sm" className="h-8 text-xs relative">
                    <SlidersHorizontal className="h-3.5 w-3.5" />
                    <span className="absolute -top-1 -right-1 flex items-center justify-center w-4 h-4 rounded-full bg-red-500 text-white text-[10px] font-medium">3</span>
                  </Button>
                </div>
              </div>
              
              {/* Active filters indicator */}
              <div className="flex flex-wrap gap-1.5">
                <Badge className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
                  <span>Category: Office Supplies</span>
                  <X className="h-3 w-3 cursor-pointer" />
                </Badge>
                <Badge className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
                  <span>Amount: > $100</span>
                  <X className="h-3 w-3 cursor-pointer" />
                </Badge>
                <Badge className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5">
                  <span>Status: Pending</span>
                  <X className="h-3 w-3 cursor-pointer" />
                </Badge>
              </div>
              
              {/* Second tier: Expandable advanced filters */}
              <div className="border border-gray-200 rounded-lg p-4 bg-white">
                <div className="text-xs font-medium mb-3 pb-2 border-b border-gray-100">Advanced Filters</div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                  {/* Category filter */}
                  <div className="space-y-1">
                    <Label className="text-xs font-medium">Categories</Label>
                    <Select defaultValue="office">
                      <SelectTrigger className="h-8 text-xs">
                        <SelectValue placeholder="Select categories" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="office">Office Supplies</SelectItem>
                        <SelectItem value="software">Software</SelectItem>
                        <SelectItem value="travel">Travel</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Payment Method filter */}
                  <div className="space-y-1">
                    <Label className="text-xs font-medium">Payment Method</Label>
                    <Select defaultValue="card">
                      <SelectTrigger className="h-8 text-xs">
                        <SelectValue placeholder="Select method" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="card">Credit Card</SelectItem>
                        <SelectItem value="cash">Cash</SelectItem>
                        <SelectItem value="bank">Bank Transfer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Amount Range filter */}
                  <div className="space-y-1">
                    <Label className="text-xs font-medium">Amount Range</Label>
                    <div className="flex gap-2 items-center">
                      <Input className="h-8 text-xs" placeholder="Min" defaultValue="100" />
                      <span className="text-xs text-gray-500">to</span>
                      <Input className="h-8 text-xs" placeholder="Max" />
                    </div>
                  </div>
                  
                  {/* Status filter */}
                  <div className="space-y-1">
                    <Label className="text-xs font-medium">Status</Label>
                    <Select defaultValue="pending">
                      <SelectTrigger className="h-8 text-xs">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="processed">Processed</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  {/* Vendor filter */}
                  <div className="space-y-1">
                    <Label className="text-xs font-medium">Vendor</Label>
                    <Input className="h-8 text-xs" placeholder="Search vendors" />
                  </div>
                  
                  {/* Date Range filter */}
                  <div className="space-y-1">
                    <Label className="text-xs font-medium">Specific Date Range</Label>
                    <div className="flex gap-2 items-center">
                      <DatePicker date={new Date()} onDateChange={() => {}} className="h-8 text-xs flex-1" placeholder="From" />
                      <span className="text-xs text-gray-500">to</span>
                      <DatePicker date={undefined} onDateChange={() => {}} className="h-8 text-xs flex-1" placeholder="To" />
                    </div>
                  </div>
                  
                  {/* Tax Deductible filter */}
                  <div className="space-y-1 flex items-center">
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="tax-deductible" className="h-4 w-4 rounded border-gray-300" />
                      <Label htmlFor="tax-deductible" className="text-xs font-medium">Tax Deductible Only</Label>
                    </div>
                  </div>
                  
                  {/* Has Receipt filter */}
                  <div className="space-y-1 flex items-center">
                    <div className="flex items-center space-x-2">
                      <input type="checkbox" id="has-receipt" className="h-4 w-4 rounded border-gray-300" />
                      <Label htmlFor="has-receipt" className="text-xs font-medium">Has Receipt Only</Label>
                    </div>
                  </div>
                </div>
                
                {/* Action buttons */}
                <div className="mt-4 pt-3 border-t border-gray-100 flex justify-end gap-2">
                  <Button variant="outline" size="sm" className="h-7 text-xs">
                    <RotateCcw className="h-3 w-3 mr-1.5" />
                    Reset
                  </Button>
                  <Button size="sm" className="h-7 text-xs">
                    Apply Filters
                  </Button>
                </div>
              </div>
            </div>
          </Container>

          {/* Table Design 1: Minimalist Clean Table */}
          <Container title="Table Design 1: Minimalist Clean Table">
            <div className="border border-gray-200 rounded-lg overflow-hidden">
              <table className="w-full text-xs">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left font-medium text-gray-900">Product</th>
                    <th className="px-4 py-3 text-left font-medium text-gray-900">Category</th>
                    <th className="px-4 py-3 text-left font-medium text-gray-900">Price</th>
                    <th className="px-4 py-3 text-left font-medium text-gray-900">Stock</th>
                    <th className="px-4 py-3 text-left font-medium text-gray-900">Status</th>
                    <th className="px-4 py-3 text-center font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  <tr className="hover:bg-gray-50">
                    <td className="px-4 py-3">
                      <div className="font-medium text-gray-900">Wireless Headphones</div>
                      <div className="text-gray-500 text-xs">SKU: WH-2023-BLK</div>
                    </td>
                    <td className="px-4 py-3">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Electronics
                      </span>
                    </td>
                    <td className="px-4 py-3 font-medium text-gray-900">$89.99</td>
                    <td className="px-4 py-3">142</td>
                    <td className="px-4 py-3">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-100">
                        In Stock
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center justify-center space-x-1">
                        <Button variant="outline" size="sm" className="h-7 w-7 p-0 border-gray-200 hover:bg-gray-50">
                          <Eye className="h-3.5 w-3.5 text-gray-600" />
                        </Button>
                        <Button variant="outline" size="sm" className="h-7 w-7 p-0 border-gray-200 hover:bg-gray-50">
                          <Edit3 className="h-3.5 w-3.5 text-gray-600" />
                        </Button>
                        <Button variant="outline" size="sm" className="h-7 w-7 p-0 border-gray-200 hover:bg-gray-50">
                          <Trash2 className="h-3.5 w-3.5 text-gray-600" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="px-4 py-3">
                      <div className="font-medium text-gray-900">Desk Lamp</div>
                      <div className="text-gray-500 text-xs">SKU: DL-2023-WHT</div>
                    </td>
                    <td className="px-4 py-3">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Home & Office
                      </span>
                    </td>
                    <td className="px-4 py-3 font-medium text-gray-900">$34.99</td>
                    <td className="px-4 py-3">56</td>
                    <td className="px-4 py-3">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-50 text-yellow-700 border border-yellow-100">
                        Low Stock
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center justify-center space-x-1">
                        <Button variant="outline" size="sm" className="h-7 w-7 p-0 border-gray-200 hover:bg-gray-50">
                          <Eye className="h-3.5 w-3.5 text-gray-600" />
                        </Button>
                        <Button variant="outline" size="sm" className="h-7 w-7 p-0 border-gray-200 hover:bg-gray-50">
                          <Edit3 className="h-3.5 w-3.5 text-gray-600" />
                        </Button>
                        <Button variant="outline" size="sm" className="h-7 w-7 p-0 border-gray-200 hover:bg-gray-50">
                          <Trash2 className="h-3.5 w-3.5 text-gray-600" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="px-4 py-3">
                      <div className="font-medium text-gray-900">Coffee Mug Set</div>
                      <div className="text-gray-500 text-xs">SKU: CMS-2023-SET</div>
                    </td>
                    <td className="px-4 py-3">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Kitchen
                      </span>
                    </td>
                    <td className="px-4 py-3 font-medium text-gray-900">$24.99</td>
                    <td className="px-4 py-3">0</td>
                    <td className="px-4 py-3">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-50 text-red-700 border border-red-100">
                        Out of Stock
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center justify-center space-x-1">
                        <Button variant="outline" size="sm" className="h-7 w-7 p-0 border-gray-200 hover:bg-gray-50">
                          <Eye className="h-3.5 w-3.5 text-gray-600" />
                        </Button>
                        <Button variant="outline" size="sm" className="h-7 w-7 p-0 border-gray-200 hover:bg-gray-50">
                          <Edit3 className="h-3.5 w-3.5 text-gray-600" />
                        </Button>
                        <Button variant="outline" size="sm" className="h-7 w-7 p-0 border-gray-200 hover:bg-gray-50">
                          <Trash2 className="h-3.5 w-3.5 text-gray-600" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              <strong>Design Features:</strong> Clean lines, ample whitespace, consistent spacing, subtle hover effects, minimalist action buttons<br />
              <strong>Use Case:</strong> General purpose tables with emphasis on readability and clean presentation
            </div>
          </Container>

          {/* Table Design 2: Compact Data-Dense Table */}
          <Container title="Table Design 2: Compact Data-Dense Table">
            <div className="border border-gray-200 rounded-lg overflow-hidden">
              <table className="w-full text-xs">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 py-2 text-left font-medium text-gray-900">ID</th>
                    <th className="px-3 py-2 text-left font-medium text-gray-900">Description</th>
                    <th className="px-3 py-2 text-left font-medium text-gray-900">Category</th>
                    <th className="px-3 py-2 text-left font-medium text-gray-900">Vendor</th>
                    <th className="px-3 py-2 text-left font-medium text-gray-900">Amount</th>
                    <th className="px-3 py-2 text-left font-medium text-gray-900">Date</th>
                    <th className="px-3 py-2 text-center font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  <tr className="hover:bg-gray-50">
                    <td className="px-3 py-2 font-medium text-gray-900">EXP-001</td>
                    <td className="px-3 py-2">
                      <div className="font-medium text-gray-900">Office supplies</div>
                      <div className="text-gray-500">Staples</div>
                    </td>
                    <td className="px-3 py-2">
                      <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Office
                      </span>
                    </td>
                    <td className="px-3 py-2 text-gray-500">Staples Inc.</td>
                    <td className="px-3 py-2 font-medium text-gray-900">$125.50</td>
                    <td className="px-3 py-2 text-gray-500">Jun 15, 2023</td>
                    <td className="px-3 py-2">
                      <div className="flex items-center justify-center space-x-1">
                        <Button variant="outline" size="sm" className="h-6 w-6 p-0 border-gray-200 hover:bg-gray-50">
                          <Eye className="h-3 w-3 text-gray-600" />
                        </Button>
                        <Button variant="outline" size="sm" className="h-6 w-6 p-0 border-gray-200 hover:bg-gray-50">
                          <Edit3 className="h-3 w-3 text-gray-600" />
                        </Button>
                        <Button variant="outline" size="sm" className="h-6 w-6 p-0 border-gray-200 hover:bg-gray-50">
                          <Trash2 className="h-3 w-3 text-gray-600" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="px-3 py-2 font-medium text-gray-900">EXP-002</td>
                    <td className="px-3 py-2">
                      <div className="font-medium text-gray-900">Software subscription</div>
                      <div className="text-gray-500">Adobe</div>
                    </td>
                    <td className="px-3 py-2">
                      <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Software
                      </span>
                    </td>
                    <td className="px-3 py-2 text-gray-500">Adobe Systems</td>
                    <td className="px-3 py-2 font-medium text-gray-900">$89.99</td>
                    <td className="px-3 py-2 text-gray-500">Jun 10, 2023</td>
                    <td className="px-3 py-2">
                      <div className="flex items-center justify-center space-x-1">
                        <Button variant="outline" size="sm" className="h-6 w-6 p-0 border-gray-200 hover:bg-gray-50">
                          <Eye className="h-3 w-3 text-gray-600" />
                        </Button>
                        <Button variant="outline" size="sm" className="h-6 w-6 p-0 border-gray-200 hover:bg-gray-50">
                          <Edit3 className="h-3 w-3 text-gray-600" />
                        </Button>
                        <Button variant="outline" size="sm" className="h-6 w-6 p-0 border-gray-200 hover:bg-gray-50">
                          <Trash2 className="h-3 w-3 text-gray-600" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="px-3 py-2 font-medium text-gray-900">EXP-003</td>
                    <td className="px-3 py-2">
                      <div className="font-medium text-gray-900">Business lunch</div>
                      <div className="text-gray-500">Restaurant XYZ</div>
                    </td>
                    <td className="px-3 py-2">
                      <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Meals
                      </span>
                    </td>
                    <td className="px-3 py-2 text-gray-500">XYZ Restaurant</td>
                    <td className="px-3 py-2 font-medium text-gray-900">$250.00</td>
                    <td className="px-3 py-2 text-gray-500">Jun 5, 2023</td>
                    <td className="px-3 py-2">
                      <div className="flex items-center justify-center space-x-1">
                        <Button variant="outline" size="sm" className="h-6 w-6 p-0 border-gray-200 hover:bg-gray-50">
                          <Eye className="h-3 w-3 text-gray-600" />
                        </Button>
                        <Button variant="outline" size="sm" className="h-6 w-6 p-0 border-gray-200 hover:bg-gray-50">
                          <Edit3 className="h-3 w-3 text-gray-600" />
                        </Button>
                        <Button variant="outline" size="sm" className="h-6 w-6 p-0 border-gray-200 hover:bg-gray-50">
                          <Trash2 className="h-3 w-3 text-gray-600" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              <strong>Design Features:</strong> Compact spacing, reduced padding, data-dense layout, efficient use of space<br />
              <strong>Use Case:</strong> Data-heavy interfaces where maximizing information density is important
            </div>
          </Container>

          {/* Table Design 3: Modern Card-Style Table */}
          <Container title="Table Design 3: Modern Card-Style Table">
            <div className="space-y-3">
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <table className="w-full text-xs">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left font-medium text-gray-900">Product Details</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-900">Inventory</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-900">Pricing</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-900">Status</th>
                      <th className="px-4 py-3 text-center font-medium text-gray-900">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    <tr className="hover:bg-gray-50">
                      <td className="px-4 py-3">
                        <div className="flex items-center">
                          <div className="bg-gray-200 border-2 border-dashed rounded-xl w-10 h-10" />
                          <div className="ml-3">
                            <div className="font-medium text-gray-900">Wireless Headphones</div>
                            <div className="text-gray-500">SKU: WH-2023-BLK</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="text-gray-900">142 units</div>
                        <div className="text-gray-500 text-xs">Threshold: 20</div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="font-medium text-gray-900">$89.99</div>
                        <div className="text-gray-500 text-xs line-through">$99.99</div>
                      </td>
                      <td className="px-4 py-3">
                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-100">
                          In Stock
                        </span>
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex items-center justify-center space-x-1">
                          <Button variant="outline" size="sm" className="h-7 w-7 p-0 border-gray-200 hover:bg-gray-50">
                            <Eye className="h-3.5 w-3.5 text-gray-600" />
                          </Button>
                          <Button variant="outline" size="sm" className="h-7 w-7 p-0 border-gray-200 hover:bg-gray-50">
                            <Edit3 className="h-3.5 w-3.5 text-gray-600" />
                          </Button>
                          <Button variant="outline" size="sm" className="h-7 w-7 p-0 border-gray-200 hover:bg-gray-50">
                            <Trash2 className="h-3.5 w-3.5 text-gray-600" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                    <tr className="hover:bg-gray-50">
                      <td className="px-4 py-3">
                        <div className="flex items-center">
                          <div className="bg-gray-200 border-2 border-dashed rounded-xl w-10 h-10" />
                          <div className="ml-3">
                            <div className="font-medium text-gray-900">Desk Lamp</div>
                            <div className="text-gray-500">SKU: DL-2023-WHT</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="text-gray-900">56 units</div>
                        <div className="text-gray-500 text-xs">Threshold: 30</div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="font-medium text-gray-900">$34.99</div>
                        <div className="text-gray-500 text-xs">Regular price</div>
                      </td>
                      <td className="px-4 py-3">
                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-50 text-yellow-700 border border-yellow-100">
                          Low Stock
                        </span>
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex items-center justify-center space-x-1">
                          <Button variant="outline" size="sm" className="h-7 w-7 p-0 border-gray-200 hover:bg-gray-50">
                            <Eye className="h-3.5 w-3.5 text-gray-600" />
                          </Button>
                          <Button variant="outline" size="sm" className="h-7 w-7 p-0 border-gray-200 hover:bg-gray-50">
                            <Edit3 className="h-3.5 w-3.5 text-gray-600" />
                          </Button>
                          <Button variant="outline" size="sm" className="h-7 w-7 p-0 border-gray-200 hover:bg-gray-50">
                            <Trash2 className="h-3.5 w-3.5 text-gray-600" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div className="mt-2 text-xs text-gray-500">
              <strong>Design Features:</strong> Visual product representation, contextual information grouping, modern badge styling, clear hierarchy<br />
              <strong>Use Case:</strong> Product catalogs and inventory management where visual context enhances understanding
            </div>
          </Container>
        </div>
      )
    }
  ]

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-xl font-bold text-gray-900">Design System</h1>
          <p className="text-xs text-gray-500">Reference for UI components and styling</p>
        </div>
      </div>

      <TabNavigation 
        tabs={tabs}
        defaultTab="components"
        enableRouting={true}
        basePath="/dashboard/design"
      />
    </div>
  )
}