'use client'

import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

/**
 * Deep linking route for /dashboard/expenses/analytics
 * Redirects to main expenses page with analytics tab parameter
 */
export default function ExpensesAnalyticsPage() {
  const router = useRouter()
  
  useEffect(() => {
    // Use router.push to maintain proper browser history
    router.push('/dashboard/expenses?tab=analytics')
  }, [router])

  // Show loading state while redirecting
  return (
    <div className="flex items-center justify-center min-h-[200px]">
      <div className="text-muted-foreground">Loading analytics...</div>
    </div>
  )
}