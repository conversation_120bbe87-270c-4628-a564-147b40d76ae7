'use client'

import { useState, useEffect } from 'react'
import { TabNavigation, TabConfig } from '@/components/ui/tab-navigation'
import { ExpensesTab } from '@/components/expenses/expenses-tab'
import { AnalyticsTab } from '@/components/expenses/analytics-tab'
import { SettingsTab } from '@/components/expenses/settings-tab'
import { type ExpenseFilters, INITIAL_FILTERS } from '@/components/expenses/expense-table-header'
import { usePersistedState } from '@/lib/use-persisted-state'

export default function ExpensesPage() {
  const [isMobile, setIsMobile] = useState(false)
  
  // Shared state for filters across tabs (for contextual analytics) - with persistence
  const [globalFilters, setGlobalFilters] = usePersistedState<ExpenseFilters>(
    'expenses-filters', 
    INITIAL_FILTERS
  )
  const [filteredExpenseIds, setFilteredExpenseIds] = useState<string[]>([])

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Tab configuration
  const tabs: TabConfig[] = [
    {
      id: 'expenses',
      label: 'Expenses',
      content: (
        <ExpensesTab 
          isMobile={isMobile} 
          onFiltersChange={setGlobalFilters}
          onFilteredExpensesChange={(expenses) => setFilteredExpenseIds(expenses.map(e => e.id))}
        />
      )
    },
    {
      id: 'analytics',
      label: 'Analytics',
      content: (
        <AnalyticsTab 
          isMobile={isMobile} 
          contextualFilters={globalFilters}
          filteredExpenseIds={filteredExpenseIds}
        />
      )
    },
    {
      id: 'settings',
      label: 'Settings',
      content: <SettingsTab isMobile={isMobile} />
    }
  ]

  return (
    <div className="space-y-4">
      <TabNavigation 
        tabs={tabs}
        defaultTab="expenses"
        isMobile={isMobile}
        enableRouting={true}
        basePath="/dashboard/expenses"
      />
    </div>
  )
}
