'use client'

import { useState, useEffect, useRef } from 'react'
import { getSupabaseClient } from '@/lib/supabase'
import { NotificationService } from '@/lib/notification-service'
import { centralizedNotificationService } from '@/lib/centralized-notification-service'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { ToastAction } from '@/components/ui/toast'
import { toast } from '@/components/ui/use-toast'
import { useToast } from '@/components/ui/use-toast'
import { 
  CheckCircle, 
  AlertTriangle, 
  Info, 
  Archive, 
  CheckCheck,
  Trash2,
  EyeOff,
  Search,
  ChevronLeft,
  ChevronRight,
  SlidersHorizontal,
  RotateCcw,
  X
} from 'lucide-react'
import { format, formatDistanceToNow, parseISO, isToday, isYesterday, isThisWeek, isThisYear } from 'date-fns'
import { cn } from '@/lib/utils'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
  is_archived: boolean
}

// Define notification types based on the database schema
type NotificationType = 'stock_adjustment' | 'low_stock' | 'out_of_stock' | 'purchase_order' | 'stock_history' | 'product_deleted' | 'all'

// Define undo action types
type UndoAction = {
  type: 'archive' | 'delete'
  notifications: Notification[]
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [readFilter, setReadFilter] = useState<'all' | 'unread' | 'read' | 'archived'>('all')
  const [typeFilter, setTypeFilter] = useState<NotificationType>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalNotifications, setTotalNotifications] = useState(0)
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([])
  const [isExpanded, setIsExpanded] = useState(false)
  const notificationsPerPage = 10
  
  const { toast: showToast } = useToast()
  const undoActionRef = useRef<UndoAction | null>(null)
  
  const supabase = getSupabaseClient()
  const notificationService = new NotificationService()

  // Load notifications
  useEffect(() => {
    loadNotifications()

    // Set up subscription using centralized service
    const unsubscribe = centralizedNotificationService.subscribe(
      (newNotification: any) => {
        // Add new notification to the top of the list only if it doesn't already exist
        setNotifications(prev => {
          // Check if notification already exists
          const exists = prev.some(notification => notification.id === newNotification.id)
          if (!exists) {
            return [newNotification, ...prev]
          }
          return prev
        })
        // Reset to first page when new notification arrives
        setCurrentPage(1)
      },
      (updatedNotification: any) => {
        // Update notification in the list
        setNotifications(prev => 
          prev.map(notification => 
            notification.id === updatedNotification.id 
              ? updatedNotification 
              : notification
          )
        )
      },
      () => {} // No delete handler needed for this component
    )

    return () => {
      unsubscribe()
    }
  }, [])

  // Apply filters when notifications or filters change
  useEffect(() => {
    let result = [...notifications]
    
    // Apply read status filter
    if (readFilter === 'unread') {
      result = result.filter(notification => !notification.is_read)
    } else if (readFilter === 'read') {
      result = result.filter(notification => notification.is_read)
    } else if (readFilter === 'archived') {
      result = result.filter(notification => notification.is_archived)
    } else {
      // For 'all', show non-archived by default
      result = result.filter(notification => !notification.is_archived)
    }
    
    // Apply type filter
    if (typeFilter !== 'all') {
      result = result.filter(notification => notification.type === typeFilter)
    }
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(notification => 
        notification.title.toLowerCase().includes(query) ||
        notification.message.toLowerCase().includes(query) ||
        notification.type.toLowerCase().includes(query)
      )
    }
    
    // Apply sorting - always newest first
    result.sort((a, b) => {
      const dateA = new Date(a.created_at).getTime()
      const dateB = new Date(b.created_at).getTime()
      return dateB - dateA // Newest first
    })
    
    setFilteredNotifications(result)
    setTotalNotifications(result.length)
    
    // Reset to first page when filters change
    setCurrentPage(1)
  }, [notifications, readFilter, typeFilter, searchQuery])

  const loadNotifications = async () => {
    try {
      setLoading(true)
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        // For now, we'll load all notifications (in a real app, you might want to implement server-side pagination)
        const notifications = await notificationService.getNotifications(user.id, 1000)
        setNotifications(notifications)
        setTotalNotifications(notifications.length)
      }
    } catch (error) {
      console.error('Error loading notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  const markAsRead = async (id: string) => {
    try {
      // Update database
      await notificationService.markAsRead(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: true } 
            : notification
        )
      )
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const markAsUnread = async (id: string) => {
    try {
      // Update database
      await notificationService.markAsUnread(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: false } 
            : notification
        )
      )
    } catch (error) {
      console.error('Error marking notification as unread:', error)
    }
  }

  const markAllAsRead = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        // Update local state immediately for instant feedback
        const updatedNotifications = notifications.map(notification => 
          !notification.is_read ? { ...notification, is_read: true } : notification
        );
        setNotifications(updatedNotifications);
        
        // Update database
        await notificationService.markAllAsRead(user.id);
        
        // Broadcast the change to other components
        centralizedNotificationService.broadcastEvent('notifications-marked-as-read', { userId: user.id });
        
        // Show confirmation toast
        showToast({
          title: "All notifications marked as read",
          description: "All your notifications have been marked as read."
        })
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      showToast({
        title: "Error",
        description: "Failed to mark all notifications as read. Please try again.",
        variant: "destructive"
      })
    }
  }

  const archiveNotification = async (id: string) => {
    try {
      // Store the notification for potential undo
      const notificationToArchive = notifications.find(n => n.id === id)
      if (notificationToArchive) {
        undoActionRef.current = {
          type: 'archive',
          notifications: [notificationToArchive]
        }
      }
      
      await notificationService.archiveNotification(id)
      
      // Show undo toast
      showToast({
        title: "Notification archived",
        description: "The notification has been archived.",
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={undoArchive}
          >
            Undo
          </ToastAction>
        ),
      })
    } catch (error) {
      console.error('Error archiving notification:', error)
    }
  }

  const unarchiveNotification = async (id: string) => {
    try {
      await notificationService.unarchiveNotification(id)
    } catch (error) {
      console.error('Error unarchiving notification:', error)
    }
  }

  const deleteNotification = async (id: string) => {
    try {
      // Store the notification for potential undo
      const notificationToDelete = notifications.find(n => n.id === id)
      if (notificationToDelete) {
        undoActionRef.current = {
          type: 'delete',
          notifications: [notificationToDelete]
        }
      }
      
      await notificationService.deleteNotification(id)
      // Remove from local state
      setNotifications(prev => prev.filter(notification => notification.id !== id))
      
      // Show undo toast
      showToast({
        title: "Notification deleted",
        description: "The notification has been deleted.",
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={undoDelete}
          >
            Undo
          </ToastAction>
        ),
      })
    } catch (error) {
      console.error('Error deleting notification:', error)
    }
  }

  // Undo functions
  const undoArchive = async () => {
    if (undoActionRef.current?.type === 'archive') {
      try {
        for (const notification of undoActionRef.current.notifications) {
          await notificationService.unarchiveNotification(notification.id)
        }
        showToast({
          title: "Undo successful",
          description: "The notification has been restored.",
        })
        undoActionRef.current = null
      } catch (error) {
        console.error('Error undoing archive:', error)
      }
    }
  }

  const undoDelete = async () => {
    if (undoActionRef.current?.type === 'delete') {
      try {
        // Restore notifications to local state
        setNotifications(prev => [...undoActionRef.current!.notifications, ...prev])
        showToast({
          title: "Undo successful",
          description: "The notification has been restored.",
        })
        undoActionRef.current = null
      } catch (error) {
        console.error('Error undoing delete:', error)
      }
    }
  }

  // Bulk operations
  const markSelectedAsRead = async () => {
    try {
      // Update database
      for (const id of selectedNotifications) {
        await notificationService.markAsRead(id)
      }
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          selectedNotifications.includes(notification.id) 
            ? { ...notification, is_read: true } 
            : notification
        )
      )
      
      setSelectedNotifications([])
    } catch (error) {
      console.error('Error marking selected notifications as read:', error)
    }
  }

  const markSelectedAsUnread = async () => {
    try {
      // Update database
      for (const id of selectedNotifications) {
        await notificationService.markAsUnread(id)
      }
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          selectedNotifications.includes(notification.id) 
            ? { ...notification, is_read: false } 
            : notification
        )
      )
      
      setSelectedNotifications([])
    } catch (error) {
      console.error('Error marking selected notifications as unread:', error)
    }
  }

  const archiveSelected = async () => {
    try {
      // Store the notifications for potential undo
      const notificationsToArchive = notifications.filter(n => selectedNotifications.includes(n.id))
      undoActionRef.current = {
        type: 'archive',
        notifications: notificationsToArchive
      }
      
      for (const id of selectedNotifications) {
        await notificationService.archiveNotification(id)
      }
      
      // Show undo toast
      showToast({
        title: `${selectedNotifications.length} notification${selectedNotifications.length !== 1 ? 's' : ''} archived`,
        description: "The notifications have been archived.",
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={undoArchive}
          >
            Undo
          </ToastAction>
        ),
      })
      
      setSelectedNotifications([])
    } catch (error) {
      console.error('Error archiving selected notifications:', error)
    }
  }

  const unarchiveSelected = async () => {
    try {
      for (const id of selectedNotifications) {
        await notificationService.unarchiveNotification(id)
      }
      setSelectedNotifications([])
    } catch (error) {
      console.error('Error unarchiving selected notifications:', error)
    }
  }

  const deleteSelected = async () => {
    try {
      // Store the notifications for potential undo
      const notificationsToDelete = notifications.filter(n => selectedNotifications.includes(n.id))
      undoActionRef.current = {
        type: 'delete',
        notifications: notificationsToDelete
      }
      
      for (const id of selectedNotifications) {
        await notificationService.deleteNotification(id)
      }
      
      // Remove from local state
      setNotifications(prev => prev.filter(notification => !selectedNotifications.includes(notification.id)))
      
      // Show undo toast
      showToast({
        title: `${selectedNotifications.length} notification${selectedNotifications.length !== 1 ? 's' : ''} deleted`,
        description: "The notifications have been deleted.",
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={undoDelete}
          >
            Undo
          </ToastAction>
        ),
      })
      
      setSelectedNotifications([])
    } catch (error) {
      console.error('Error deleting selected notifications:', error)
    }
  }

  const toggleSelectAll = () => {
    if (selectedNotifications.length === currentNotifications.length) {
      setSelectedNotifications([])
    } else {
      setSelectedNotifications(currentNotifications.map(n => n.id))
    }
  }

  const toggleSelectNotification = (id: string) => {
    if (selectedNotifications.includes(id)) {
      setSelectedNotifications(selectedNotifications.filter(notificationId => notificationId !== id))
    } else {
      setSelectedNotifications([...selectedNotifications, id])
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'product_deleted':
        return <Trash2 className="h-5 w-5 text-red-500" />
      case 'info':
      default:
        return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  const getNotificationColor = (type: string, isRead: boolean) => {
    if (isRead) {
      return 'bg-gray-50 border-gray-200'
    }
    
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200'
      case 'product_deleted':
        return 'bg-red-50 border-red-200'
      case 'info':
      default:
        return 'bg-blue-50 border-blue-200'
    }
  }

  // Get human-readable name for notification type
  const getNotificationTypeName = (type: string) => {
    switch (type) {
      case 'stock_adjustment':
        return 'Stock Adjustment'
      case 'low_stock':
        return 'Low Stock'
      case 'out_of_stock':
        return 'Out of Stock'
      case 'purchase_order':
        return 'Purchase Order'
      case 'stock_history':
        return 'Stock History'
      case 'product_deleted':
        return 'Product Deleted'
      default:
        return type
    }
  }

  // Clear all filters
  const clearAllFilters = () => {
    setTypeFilter('all')
    setSearchQuery('')
  }

  // Calculate pagination
  const totalPages = Math.ceil(totalNotifications / notificationsPerPage)
  const startIndex = (currentPage - 1) * notificationsPerPage
  const endIndex = Math.min(startIndex + notificationsPerPage, filteredNotifications.length)
  const currentNotifications = filteredNotifications.slice(startIndex, endIndex)

  // Count active filters
  const activeFilterCount = (() => {
    let count = 0
    if (searchQuery.trim()) count++
    if (typeFilter !== 'all') count++
    return count
  })()

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-xl font-bold text-gray-900">Notifications</h1>
          <p className="text-xs text-gray-500 mt-1">
            Manage your notifications and alerts
          </p>
        </div>
      </div>

      {/* Search bar and filter button outside the container */}
      <div className="flex flex-col sm:flex-row gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-500" />
          <Input
            placeholder="Search notifications..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8 h-8 text-xs"
          />
        </div>
        <div className="flex items-center gap-2">
          <Select 
            value={readFilter} 
            onValueChange={(value: 'all' | 'unread' | 'read' | 'archived') => setReadFilter(value)}
          >
            <SelectTrigger className="h-8 text-xs w-[110px]">
              <SelectValue placeholder="All" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all" className="text-xs">All</SelectItem>
              <SelectItem value="unread" className="text-xs">Unread</SelectItem>
              <SelectItem value="read" className="text-xs">Read</SelectItem>
              <SelectItem value="archived" className="text-xs">Archived</SelectItem>
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            className="h-8 text-xs"
            onClick={markAllAsRead}
            disabled={notifications.filter(n => !n.is_read).length === 0}
          >
            <CheckCheck className="h-3.5 w-3.5 mr-1.5" />
            Mark all as read
          </Button>
          
          <Button 
            variant="outline" 
            size="sm" 
            className="h-8 text-xs relative"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <SlidersHorizontal className="h-3.5 w-3.5" />
            {activeFilterCount > 0 && (
              <span className="absolute -top-1 -right-1 flex items-center justify-center w-4 h-4 rounded-full bg-red-500 text-white text-[10px] font-medium">
                {activeFilterCount}
              </span>
            )}
          </Button>
        </div>
      </div>
      
      {/* Active filters indicator */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-1.5">
          {searchQuery.trim() && (
            <div className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5 rounded">
              Search: "{searchQuery}"
              <Button 
                variant="ghost" 
                size="sm" 
                className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
                onClick={() => setSearchQuery('')}
              >
                <X className="h-3 w-3 cursor-pointer" />
              </Button>
            </div>
          )}
          {typeFilter !== 'all' && (
            <div className="bg-blue-50 text-blue-700 border-none text-xs py-1 px-2 flex items-center gap-1.5 rounded">
              Type: {getNotificationTypeName(typeFilter)}
              <Button 
                variant="ghost" 
                size="sm" 
                className="ml-1 h-3.5 w-3.5 p-0 hover:bg-blue-100"
                onClick={() => setTypeFilter('all')}
              >
                <X className="h-3 w-3 cursor-pointer" />
              </Button>
            </div>
          )}
        </div>
      )}
      
      {/* Expandable advanced filters */}
      {isExpanded && (
        <div className="border border-gray-200 rounded-lg p-4 bg-white">
          <div className="text-xs font-medium mb-3 pb-2 border-b border-gray-100">Advanced Filters</div>
          <div className="grid grid-cols-1 gap-3">
            {/* Type filter */}
            <div className="space-y-1">
              <Label className="text-xs font-medium">Notification Type</Label>
              <Select 
                value={typeFilter} 
                onValueChange={(value: NotificationType) => setTypeFilter(value)}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select notification type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all" className="text-xs">All Types</SelectItem>
                  <SelectItem value="stock_adjustment" className="text-xs">Stock Adjustment</SelectItem>
                  <SelectItem value="low_stock" className="text-xs">Low Stock</SelectItem>
                  <SelectItem value="out_of_stock" className="text-xs">Out of Stock</SelectItem>
                  <SelectItem value="purchase_order" className="text-xs">Purchase Order</SelectItem>
                  <SelectItem value="stock_history" className="text-xs">Stock History</SelectItem>
                  <SelectItem value="product_deleted" className="text-xs">Product Deleted</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="mt-4 flex justify-end gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="h-7 text-xs"
              onClick={clearAllFilters}
              disabled={activeFilterCount === 0}
            >
              <RotateCcw className="h-3 w-3 mr-1.5" />
              Reset
            </Button>
            <Button 
              size="sm" 
              className="h-7 text-xs"
              onClick={() => setIsExpanded(false)}
            >
              Apply Filters
            </Button>
          </div>
        </div>
      )}

      <div className="bg-white rounded-lg border border-gray-200">
        {loading ? (
          <div className="p-8 text-center">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] text-blue-600 motion-reduce:animate-[spin_1.5s_linear_infinite]" />
            <p className="mt-2 text-sm text-gray-500">Loading notifications...</p>
          </div>
        ) : filteredNotifications.length === 0 ? (
          <div className="p-8 text-center">
            <Info className="h-12 w-12 text-gray-300 mx-auto" />
            <h3 className="mt-4 text-sm font-medium text-gray-900">No notifications</h3>
            <p className="mt-1 text-sm text-gray-500">
              {readFilter === 'all' && typeFilter === 'all' && !searchQuery
                ? 'You have no notifications at this time.' 
                : readFilter === 'archived' && typeFilter === 'all' && !searchQuery
                  ? 'You have no archived notifications.'
                : readFilter !== 'all' && typeFilter !== 'all' && searchQuery
                  ? `No ${readFilter} notifications of type ${getNotificationTypeName(typeFilter)} found matching "${searchQuery}".`
                  : readFilter !== 'all' && typeFilter !== 'all'
                    ? `You have no ${readFilter} notifications of type ${getNotificationTypeName(typeFilter)}.`
                    : readFilter !== 'all' && searchQuery
                      ? `No ${readFilter} notifications found matching "${searchQuery}".`
                      : typeFilter !== 'all' && searchQuery
                        ? `No notifications of type ${getNotificationTypeName(typeFilter)} found matching "${searchQuery}".`
                        : readFilter !== 'all'
                          ? `You have no ${readFilter} notifications.`
                          : typeFilter !== 'all'
                            ? `You have no notifications of type ${getNotificationTypeName(typeFilter)}.`
                            : `No notifications found matching "${searchQuery}".`}
            </p>
          </div>
        ) : (
          // Regular list view
          <>
            {/* Bulk Operations Bar */}
            {selectedNotifications.length > 0 && (
              <div className="bg-blue-50 border-b border-blue-100 px-4 py-3 flex items-center justify-between">
                <div className="text-sm text-blue-800">
                  {selectedNotifications.length} notification{selectedNotifications.length !== 1 ? 's' : ''} selected
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={markSelectedAsRead}
                  >
                    Mark as read
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={markSelectedAsUnread}
                  >
                    Mark as unread
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={archiveSelected}
                  >
                    <Archive className="h-4 w-4 mr-1" />
                    Archive
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={unarchiveSelected}
                  >
                    <EyeOff className="h-4 w-4 mr-1" />
                    Unarchive
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={deleteSelected}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Delete
                  </Button>
                </div>
              </div>
            )}
            
            <div className="max-h-[calc(100vh-350px)] overflow-y-auto">
              <ul className="divide-y divide-gray-200">
                {currentNotifications.map((notification) => (
                  <li 
                    key={notification.id}
                    className={cn(
                      "p-4 hover:bg-gray-50 transition-colors flex items-start",
                      getNotificationColor(notification.type, notification.is_read)
                    )}
                  >
                    <div className="pt-1 mr-3">
                      <Checkbox
                        checked={selectedNotifications.includes(notification.id)}
                        onCheckedChange={() => toggleSelectNotification(notification.id)}
                      />
                    </div>
                    <div className="flex-shrink-0 pt-0.5">
                      {getNotificationIcon(notification.type)}
                    </div>
                    <div className="ml-3 flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className={cn(
                          "text-sm font-medium",
                          notification.is_read ? "text-gray-700" : "text-gray-900"
                        )}>
                          {notification.title}
                        </p>
                        <p className="text-xs text-gray-500 whitespace-nowrap ml-2">
                          {formatDistanceToNow(parseISO(notification.created_at), { addSuffix: true })}
                        </p>
                      </div>
                      <div className="mt-1 flex items-center gap-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                          {getNotificationTypeName(notification.type)}
                        </span>
                        <p className="text-sm text-gray-600">
                          {notification.message}
                        </p>
                      </div>
                      <div className="mt-3 flex items-center gap-2">
                        {!notification.is_read ? (
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-7 text-xs"
                            onClick={() => markAsRead(notification.id)}
                          >
                            Mark as read
                          </Button>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-7 text-xs"
                            onClick={() => markAsUnread(notification.id)}
                          >
                            Mark as unread
                          </Button>
                        )}
                        {!notification.is_archived ? (
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-7 text-xs"
                            onClick={() => archiveNotification(notification.id)}
                          >
                            <Archive className="h-3 w-3 mr-1" />
                            Archive
                          </Button>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-7 text-xs"
                            onClick={() => unarchiveNotification(notification.id)}
                          >
                            <EyeOff className="h-3 w-3 mr-1" />
                            Unarchive
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-7 text-xs"
                          onClick={() => deleteNotification(notification.id)}
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </>
        )}
        
        {/* Pagination */}
        {filteredNotifications.length > notificationsPerPage && (
          <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="text-sm text-gray-700">
              Showing <span className="font-medium">{startIndex + 1}</span> to <span className="font-medium">{endIndex}</span> of{' '}
              <span className="font-medium">{totalNotifications}</span> results
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <div className="text-sm text-gray-700">
                Page <span className="font-medium">{currentPage}</span> of <span className="font-medium">{totalPages}</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}