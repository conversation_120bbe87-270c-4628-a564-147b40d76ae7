'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Package, DollarSign, ShoppingCart, TrendingUp } from 'lucide-react'
import { getSupabaseClient, type ExpenseRow } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'

interface DashboardMetrics {
  totalProducts: number
  monthlyRevenue: number
  lowStockItems: number
  totalExpenses: number
  expenseCount: number
}

export default function DashboardPage() {
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    totalProducts: 0,
    monthlyRevenue: 0,
    lowStockItems: 0,
    totalExpenses: 0,
    expenseCount: 0
  })
  const [isLoading, setIsLoading] = useState(true)
  const [recentExpenses, setRecentExpenses] = useState<ExpenseRow[]>([])
  const [businessName, setBusinessName] = useState('')
  
  const { user } = useAuth()
  const { formatCurrency } = useCurrency()
  const supabase = getSupabaseClient()

  const loadDashboardMetrics = async () => {
    if (!user?.id) return
    
    setIsLoading(true)
    try {
      console.log('Loading dashboard metrics for user:', user.id)
      
      // Fetch user profile to get business name
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('business_name')
        .eq('id', user.id)
        .single()
      
      if (!profileError && profileData) {
        setBusinessName(profileData.business_name || '')
      }
      
      // Fetch all expenses for the user
      const { data: allExpenses, error: expenseError } = await supabase
        .from('expenses')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
      
      if (expenseError) {
        console.error('Error fetching expenses:', expenseError)
        return
      }
      
      console.log('All expenses fetched:', allExpenses?.length || 0)
      
      // Calculate total expenses
      const totalExpenses = allExpenses?.reduce((sum, expense) => sum + (expense.amount || 0), 0) || 0
      
      console.log('Calculated metrics:', {
        totalExpenses,
        expenseCount: allExpenses?.length || 0
      })
      
      // Get recent expenses (last 5)
      const recentExpenseData = allExpenses?.slice(0, 5) || []
      
      // Fetch products data with stock information
      const { data: productsData, error: productsError } = await supabase
        .from('products')
        .select(`
          id,
          stock_quantity,
          low_stock_threshold,
          has_variants,
          product_variants(
            stock_quantity,
            low_stock_threshold
          )
        `)
        .eq('user_id', user.id)
      
      let totalProducts = 0
      let lowStockItems = 0
      
      if (!productsError && productsData) {
        totalProducts = productsData.length
        
        // Calculate low stock items
        productsData.forEach(product => {
          if (product.has_variants && product.product_variants) {
            // For variable products, check variants
            product.product_variants.forEach(variant => {
              const stockQty = variant.stock_quantity || 0
              const lowStockThreshold = variant.low_stock_threshold || 10
              if (stockQty > 0 && stockQty <= lowStockThreshold) {
                lowStockItems++
              }
            })
          } else {
            // For simple products, check the product itself
            const stockQty = product.stock_quantity || 0
            const lowStockThreshold = product.low_stock_threshold || 10
            if (stockQty > 0 && stockQty <= lowStockThreshold) {
              lowStockItems++
            }
          }
        })
      }
      
      if (productsError) {
        console.error('Error fetching products:', productsError)
      }
      
      setMetrics({
        totalProducts,
        monthlyRevenue: 0, // TODO: Implement when sales module is ready
        lowStockItems,
        totalExpenses,
        expenseCount: allExpenses?.length || 0
      })
      
      setRecentExpenses(recentExpenseData)
      
    } catch (error) {
      console.error('Error loading dashboard metrics:', error)
    } finally {
      setIsLoading(false)
    }
  }
  
  useEffect(() => {
    console.log('Dashboard: User state changed:', user?.id)
    if (user) {
      loadDashboardMetrics()
    }
  }, [user])
  
  // Listen for expense additions to refresh dashboard
  useEffect(() => {
    const handleExpenseAdded = (event: CustomEvent) => {
      console.log('Dashboard: Expense added event received:', event.detail)
      // Refresh dashboard metrics
      if (user) {
        setTimeout(() => {
          loadDashboardMetrics()
        }, 500) // Small delay to ensure data is persisted
      }
    }
    
    window.addEventListener('expenseAdded', handleExpenseAdded as EventListener)
    
    return () => {
      window.removeEventListener('expenseAdded', handleExpenseAdded as EventListener)
    }
  }, [user])
  
  return (
    <div className="space-y-4">
      {/* Modern Business Name Header */}
      <div className="bg-gradient-to-r from-primary to-blue-500 rounded-lg shadow-md overflow-hidden">
        <div className="px-6 py-3">
          <h1 className="text-xl text-white font-medium mb-1">
            {businessName ? (
              <>
                <span className="font-normal opacity-80">Welcome to</span> {businessName}
              </>
            ) : (
              'Dashboard'
            )}
          </h1>
          <p className="text-blue-100 text-sm">Your business management dashboard</p>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
            <CardTitle className="text-sm font-medium">Total Products</CardTitle>
            <div className="h-6 w-6 rounded-md bg-blue-50 flex items-center justify-center">
              <Package className="h-3 w-3 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="p-6 pt-0">
            <div className="text-2xl font-bold">{isLoading ? '' : metrics.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.totalProducts === 0 ? 'No products added yet' : 'Products in inventory'}
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <div className="h-6 w-6 rounded-md bg-blue-50 flex items-center justify-center">
              <DollarSign className="h-3 w-3 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="p-6 pt-0">
            <div className="text-2xl font-bold">
              {isLoading ? '...' : formatCurrency(metrics.monthlyRevenue)}
            </div>
            <p className="text-xs text-muted-foreground">
              {metrics.monthlyRevenue === 0 ? 'Sales tracking coming soon' : 'Total revenue'}
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
            <div className="h-6 w-6 rounded-md bg-blue-50 flex items-center justify-center">
              <ShoppingCart className="h-3 w-3 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="p-6 pt-0">
            <div className="text-2xl font-bold">{isLoading ? '...' : metrics.lowStockItems}</div>
            <p className="text-xs text-muted-foreground">
              {metrics.lowStockItems === 0 ? 'All items in stock' : 'Items need restocking'}
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
            <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
            <div className="h-6 w-6 rounded-md bg-blue-50 flex items-center justify-center">
              <TrendingUp className="h-3 w-3 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="p-6 pt-0">
            <div className="text-2xl font-bold">
              {isLoading ? '...' : formatCurrency(metrics.totalExpenses)}
            </div>
            <p className="text-xs text-muted-foreground">
              {metrics.expenseCount === 0 ? 'No expenses recorded' : `${metrics.expenseCount} total expenses`}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Get started with common tasks
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <a
                href="/dashboard/products"
                className="flex items-center justify-center p-4 border border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors"
              >
                <div className="text-center">
                  <Package className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                  <p className="text-sm font-medium text-gray-900">Add Product</p>
                  <p className="text-xs text-gray-500">Start building your inventory</p>
                </div>
              </a>
              
              <a
                href="/dashboard/expenses"
                className="flex items-center justify-center p-4 border border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors"
              >
                <div className="text-center">
                  <DollarSign className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                  <p className="text-sm font-medium text-gray-900">Track Expenses</p>
                  <p className="text-xs text-gray-500">Record business expenses</p>
                </div>
              </a>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Expenses</CardTitle>
            <CardDescription>
              Latest expense entries
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-3">
                {[1, 2, 3].map(i => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                ))}
              </div>
            ) : recentExpenses.length === 0 ? (
              <div className="text-center py-6">
                <TrendingUp className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500">No expenses recorded yet</p>
                <p className="text-xs text-gray-400">Start tracking your business expenses</p>
              </div>
            ) : (
              <div className="space-y-3">
                {recentExpenses.map((expense, index) => (
                  <div key={expense.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {expense.description || 'No description'}
                        </p>
                        <span className="text-sm font-semibold text-red-600">
                          {formatCurrency(expense.amount)}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                          {expense.category}
                        </span>
                        {expense.vendor && (
                          <span className="text-xs text-gray-500">
                            {expense.vendor}
                          </span>
                        )}
                        <span className="text-xs text-gray-400">
                          {new Date(expense.expense_date || expense.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
                {recentExpenses.length > 0 && (
                  <div className="text-center pt-2">
                    <a 
                      href="/dashboard/expenses" 
                      className="text-sm text-primary hover:text-blue-800"
                    >
                      View all expenses →
                    </a>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Getting Started Section */}
      <Card>
        <CardHeader>
          <CardTitle>Getting Started</CardTitle>
          <CardDescription>
            Steps to set up your business
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  metrics.totalProducts > 0 ? 'bg-green-100' : 'bg-blue-100'
                }`}>
                  <span className={`text-sm font-medium ${
                    metrics.totalProducts > 0 ? 'text-green-600' : 'text-primary'
                  }`}>1</span>
                </div>
              </div>
              <div>
                <p className={`text-sm ${
                  metrics.totalProducts > 0 ? 'text-green-900 font-medium' : 'text-gray-900'
                }`}>Add your first product</p>
                <p className="text-xs text-gray-500">Build your inventory</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
                  <span className="text-sm font-medium text-gray-600">2</span>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500">Set up product categories</p>
                <p className="text-xs text-gray-400">Organize your products</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  metrics.expenseCount > 0 ? 'bg-green-100' : 'bg-gray-100'
                }`}>
                  <span className={`text-sm font-medium ${
                    metrics.expenseCount > 0 ? 'text-green-600' : 'text-gray-600'
                  }`}>3</span>
                </div>
              </div>
              <div>
                <p className={`text-sm ${
                  metrics.expenseCount > 0 ? 'text-green-900 font-medium' : 'text-gray-500'
                }`}>Start tracking expenses</p>
                <p className="text-xs text-gray-400">
                  {metrics.expenseCount > 0 ? `${metrics.expenseCount} expenses recorded` : 'Record business costs'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-full">
                  <span className="text-sm font-medium text-gray-600">4</span>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500">Generate your first report</p>
                <p className="text-xs text-gray-400">Analyze your data</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}