'use client'

import { useState, useEffect } from 'react'
import { InventoryTab } from '@/components/products/products-catalog/inventory-tab'
import { usePersistedState } from '@/lib/use-persisted-state'
import { type ProductFilters, INITIAL_PRODUCT_FILTERS } from '@/components/products/products-catalog/product-filters'
import { Button } from '@/components/ui/button'
import { FileSpreadsheet } from 'lucide-react'

export default function InventoryPage() {
  const [isMobile, setIsMobile] = useState(false)
  
  // Shared state for filters (for contextual analytics) - with persistence
  const [globalFilters, setGlobalFilters] = usePersistedState<ProductFilters>(
    'products-filters', 
    INITIAL_PRODUCT_FILTERS
  )
  const [filteredProductIds, setFilteredProductIds] = useState<string[]>([])
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [isExporting, setIsExporting] = useState(false)

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Refresh inventory data
  const handleRefresh = () => {
    // This will trigger a refresh in the InventoryTab component
    window.dispatchEvent(new CustomEvent('refreshInventory'))
  }

  // Export to CSV function
  const exportToCSV = () => {
    setIsExporting(true)
    // Dispatch event to trigger export in InventoryTab
    window.dispatchEvent(new CustomEvent('exportInventoryCSV'))
  }

  return (
    <div className="space-y-6">
      {/* Page Header with Title and Actions */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-xl font-bold text-gray-900">Inventory</h1>
          <p className="text-xs text-gray-500 mt-1">Track stock levels, manage adjustments, and prevent stockouts</p>
        </div>
        
        <div className="flex flex-col sm:flex-row sm:items-center gap-2">
          {/* Export Button */}
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-1.5 h-8 px-3"
            onClick={exportToCSV}
          >
            <FileSpreadsheet className="h-4 w-4" />
            <span>Export CSV</span>
          </Button>
        </div>
      </div>

      <InventoryTab />
    </div>
  )
}
