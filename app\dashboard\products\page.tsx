'use client'

import { useState, useEffect } from 'react'
import { ProductsTab } from '@/components/products/products-catalog/products-tab'
import { ProductsErrorBoundary } from '@/components/products/products-catalog/products-error-boundary'
import { type ProductFilters, INITIAL_PRODUCT_FILTERS } from '@/components/products/products-catalog/product-filters'
import { AddProductFormV2 } from '@/components/products/add-product-v2/AddProductFormV2'
import { useToast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { FileSpreadsheet } from 'lucide-react'
import { usePersistedState } from '@/lib/use-persisted-state'

export default function ProductsPage() {
  const [isMobile, setIsMobile] = useState(false)
  const [isAddProductModalOpen, setIsAddProductModalOpen] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  
  // Shared state for filters across tabs (for contextual analytics) - with persistence
  const [globalFilters, setGlobalFilters] = usePersistedState<ProductFilters>(
    'products-filters', 
    INITIAL_PRODUCT_FILTERS
  )
  const [filteredProductIds, setFilteredProductIds] = useState<string[]>([])
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [filteredProducts, setFilteredProducts] = useState<any[]>([])
  const { toast } = useToast()

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const handleProductAdded = () => {
    // Close the modal after successful product addition
    setIsAddProductModalOpen(false)
  }

  // Export to CSV function
  const exportToCSV = () => {
    setIsExporting(true)
    
    try {
      // Create CSV header with metadata
      const metadataLines = [
        '# Product Catalog Report',
        `# Generated on: ${new Date().toISOString()}`,
        `# Total Products: ${filteredProducts.length}`,
        '#',
        '# Report Data:'
      ]
      
      // CSV header row
      const headers = [
        'Product Name', 'SKU', 'Category', 'Supplier', 'Type', 'Size', 'Color',
        'Cost', 'Price', 'Sale Price', 'Effective Price', 'Stock', 'Low Stock Alert',
        'Stock Status', 'Created At'
      ]
      
      // CSV data rows with proper escaping
      const dataRows = filteredProducts.map(product => {
        const stockQty = product.stock_quantity || 0
        const lowStockThreshold = product.low_stock_threshold || 10
        const stockStatus = stockQty === 0 ? 'Out of Stock' : 
                           stockQty <= lowStockThreshold ? 'Low Stock' : 'In Stock'
        const createdDate = new Date(product.created_at)
        
        return [
          `"${(product.name || '').replace(/"/g, '""')}"`,
          product.base_sku || '',
          product.category_name || '',
          `"${(product.supplier || '').replace(/"/g, '""')}"`,
          product.has_variants ? 'Variable' : 'Simple',
          product.size || '',
          product.color || '',
          (product.total_cost || 0).toFixed(2),
          (product.price || 0).toFixed(2),
          (product.sale_price || '').toString(),
          (product.effective_price || product.price || 0).toFixed(2),
          stockQty,
          lowStockThreshold,
          stockStatus,
          createdDate.toISOString()
        ].join(',')
      })
      
      // Combine all parts
      const csvContent = [
        ...metadataLines,
        headers.join(','),
        ...dataRows
      ].join('\n')

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `products-export-${new Date().toISOString().split('T')[0]}.csv`
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toast({
        title: "CSV Export Complete",
        description: `Successfully exported ${filteredProducts.length} products to CSV format.`,
      })
    } catch (error) {
      console.error('CSV export error:', error)
      toast({
        title: "CSV Export Failed",
        description: "Failed to export products. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header with Title and Actions */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-xl font-bold text-gray-900">Products Catalog</h1>
          <p className="text-xs text-gray-500 mt-1">Manage your product catalog</p>
        </div>
        
        <div className="flex flex-col sm:flex-row sm:items-center gap-2">
          {/* Export Button */}
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-1.5 h-8 px-3"
            onClick={exportToCSV}
            disabled={isExporting || filteredProducts.length === 0}
          >
            <FileSpreadsheet className="h-4 w-4" />
            <span>Export CSV</span>
          </Button>
          
          {/* Add Product Button */}
          <Button
            className="flex items-center gap-1.5 h-8 px-3 bg-blue-600 hover:bg-blue-700"
            onClick={() => setIsAddProductModalOpen(true)}
          >
            <span>+ Add Product</span>
          </Button>
        </div>
      </div>
      
      {/* Main Content - Products Tab without inline Add Product Form */}
      <ProductsErrorBoundary>
        <ProductsTab
          isMobile={isMobile}
          onFiltersChange={setGlobalFilters}
          onFilteredProductsChange={(products) => {
            setFilteredProductIds(products.map(p => p.id));
            setFilteredProducts(products);
          }}
          onSelectedProductsChange={setSelectedProducts}
        />
      </ProductsErrorBoundary>
      
      {/* Add Product Modal (New Modular Form) */}
      <AddProductFormV2
        open={isAddProductModalOpen}
        onOpenChange={setIsAddProductModalOpen}
        onProductAdded={handleProductAdded}
      />
    </div>
  )
}