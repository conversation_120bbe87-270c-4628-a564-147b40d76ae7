'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  <PERSON>alog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { getSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { 
  getPurchaseOrderById, 
  updatePurchaseOrderItemReceived 
} from '@/components/products/purchase-orders/purchase-order-service'
// formatCurrency is now accessed through the useCurrency hook
import { useCurrency } from '@/contexts/currency-context'
import { 
  ArrowLeft, 
  Package, 
  Calendar, 
  DollarSign, 
  Truck,
  CheckCircle
} from 'lucide-react'
import { ExtendedPurchaseOrder, PurchaseOrderItem } from '@/components/products/purchase-orders/types'

export default function PurchaseOrderDetailPage() {
  const { id } = useParams()
  const router = useRouter()
  const { user } = useAuth()
  const { toast } = useToast()
  const { userCurrency, formatCurrency } = useCurrency()
  
  const [purchaseOrder, setPurchaseOrder] = useState<ExtendedPurchaseOrder | null>(null)
  const [loading, setLoading] = useState(true)
  const [isReceiveModalOpen, setIsReceiveModalOpen] = useState(false)
  const [selectedItem, setSelectedItem] = useState<PurchaseOrderItem | null>(null)
  const [receiveQuantity, setReceiveQuantity] = useState(0)
  
  // Fetch purchase order details
  const fetchPurchaseOrder = async () => {
    if (!user || !id) return
    
    setLoading(true)
    try {
      const po = await getPurchaseOrderById(id as string, user.id)
      setPurchaseOrder(po)
    } catch (error) {
      console.error('Error fetching purchase order:', error)
      toast({
        title: "Error",
        description: "Failed to fetch purchase order details. Please try again.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }
  
  // Handle receive stock
  const handleReceiveStock = (item: PurchaseOrderItem) => {
    setSelectedItem(item)
    setReceiveQuantity(item.quantity_ordered - item.quantity_received)
    setIsReceiveModalOpen(true)
  }
  
  // Submit receive stock
  const handleSubmitReceive = async () => {
    if (!user || !selectedItem) return
    
    try {
      // Validate quantity
      if (receiveQuantity < 0 || receiveQuantity > (selectedItem.quantity_ordered - selectedItem.quantity_received)) {
        toast({
          title: "Invalid Quantity",
          description: "Please enter a valid quantity to receive.",
          variant: "destructive"
        })
        return
      }
      
      // Update the item
      const newReceivedQuantity = selectedItem.quantity_received + receiveQuantity
      await updatePurchaseOrderItemReceived(selectedItem.id, user.id, newReceivedQuantity)
      
      toast({
        title: "Success",
        description: `Received ${receiveQuantity} units of ${selectedItem.product_name}`
      })
      
      // Close modal and refresh data
      setIsReceiveModalOpen(false)
      fetchPurchaseOrder()
    } catch (error) {
      console.error('Error receiving stock:', error)
      toast({
        title: "Error",
        description: "Failed to receive stock. Please try again.",
        variant: "destructive"
      })
    }
  }
  
  // Fetch data on mount and when ID changes
  useEffect(() => {
    fetchPurchaseOrder()
  }, [id, user])
  
  // Set up real-time subscription
  useEffect(() => {
    if (!user || !id) return
    
    const supabase = getSupabaseClient()
    
    const poChannel = supabase
      .channel('purchase_order_changes')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'purchase_orders',
          filter: `id=eq.${id}`
        },
        () => {
          fetchPurchaseOrder()
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'purchase_order_items',
          filter: `purchase_order_id=eq.${id}`
        },
        () => {
          fetchPurchaseOrder()
        }
      )
      .subscribe()
    
    // Cleanup subscription on unmount
    return () => {
      supabase.removeChannel(poChannel)
    }
  }, [id, user])
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }
  
  if (!purchaseOrder) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Package className="h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-1">Purchase Order Not Found</h3>
        <p className="text-sm text-gray-500 mb-4">The purchase order you're looking for doesn't exist or has been deleted.</p>
        <Button onClick={() => router.push('/dashboard/products/purchase-orders')}>
          Back to Purchase Orders
        </Button>
      </div>
    )
  }
  
  const totalReceived = purchaseOrder.items.reduce((sum, item) => sum + item.quantity_received, 0)
  const totalOrdered = purchaseOrder.items.reduce((sum, item) => sum + item.quantity_ordered, 0)
  const remainingToReceive = totalOrdered - totalReceived
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button 
            variant="ghost" 
            size="sm" 
            className="mr-3 h-8 w-8 p-0"
            onClick={() => router.push('/dashboard/products/purchase-orders')}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-xl font-bold text-gray-900">{purchaseOrder.po_number}</h1>
            <p className="text-xs text-gray-500">Purchase Order Details</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${purchaseOrder.status_color}`}>
            {purchaseOrder.status_display}
          </span>
        </div>
      </div>
      
      {/* PO Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="rounded-full bg-blue-100 p-2 mr-3">
              <Package className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-xs text-gray-500">Total Items</p>
              <p className="text-lg font-semibold">{totalOrdered}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="rounded-full bg-green-100 p-2 mr-3">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-xs text-gray-500">Received</p>
              <p className="text-lg font-semibold">{totalReceived}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center">
            <div className="rounded-full bg-yellow-100 p-2 mr-3">
              <Truck className="h-5 w-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-xs text-gray-500">Remaining</p>
              <p className="text-lg font-semibold">{remainingToReceive}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="space-y-4">
            <div className="flex items-center">
              <div className="rounded-full bg-purple-100 p-2 mr-3">
                <DollarSign className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-xs text-gray-500">Total Value</p>
                <p className="text-lg font-semibold">{formatCurrency(purchaseOrder.total_value, userCurrency)}</p>
              </div>
            </div>

            {/* Financial Breakdown */}
            {(purchaseOrder.subtotal || purchaseOrder.tax_amount || purchaseOrder.shipping_cost || purchaseOrder.other_charges || purchaseOrder.discount_amount) && (
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 space-y-2">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Financial Breakdown</h4>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Subtotal</span>
                    <span className="font-medium">{formatCurrency(purchaseOrder.subtotal || 0, userCurrency)}</span>
                  </div>

                  {purchaseOrder.tax_amount > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Tax ({purchaseOrder.tax_rate}%)</span>
                      <span className="font-medium">{formatCurrency(purchaseOrder.tax_amount, userCurrency)}</span>
                    </div>
                  )}

                  {purchaseOrder.shipping_cost > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Shipping</span>
                      <span className="font-medium">{formatCurrency(purchaseOrder.shipping_cost, userCurrency)}</span>
                    </div>
                  )}

                  {purchaseOrder.other_charges > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Other Charges</span>
                      <span className="font-medium">{formatCurrency(purchaseOrder.other_charges, userCurrency)}</span>
                    </div>
                  )}

                  {purchaseOrder.discount_amount > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Discount</span>
                      <span className="font-medium text-red-600">-{formatCurrency(purchaseOrder.discount_amount, userCurrency)}</span>
                    </div>
                  )}

                  <div className="border-t border-gray-200 dark:border-gray-600 pt-2 mt-2">
                    <div className="flex justify-between font-semibold">
                      <span className="text-gray-900 dark:text-gray-100">Total</span>
                      <span className="text-purple-600 dark:text-purple-400">{formatCurrency(purchaseOrder.total_value, userCurrency)}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* PO Details */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Purchase Order Details</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm font-medium text-gray-900 mb-3">Supplier Information</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-xs text-gray-500">Supplier</span>
                <span className="text-xs font-medium">{purchaseOrder.supplier}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-xs text-gray-500">Reference Number</span>
                <span className="text-xs font-medium">{purchaseOrder.reference_number || '-'}</span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-900 mb-3">Dates</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-xs text-gray-500">Issue Date</span>
                <span className="text-xs font-medium flex items-center">
                  <Calendar className="h-3 w-3 mr-1" />
                  {purchaseOrder.formatted_issue_date}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-xs text-gray-500">Expected Arrival</span>
                <span className="text-xs font-medium flex items-center">
                  <Calendar className="h-3 w-3 mr-1" />
                  {purchaseOrder.formatted_expected_arrival_date || '-'}
                </span>
              </div>
              {purchaseOrder.formatted_received_date && (
                <div className="flex justify-between">
                  <span className="text-xs text-gray-500">Received Date</span>
                  <span className="text-xs font-medium flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    {purchaseOrder.formatted_received_date}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
        
        {purchaseOrder.notes && (
          <div className="mt-6">
            <h3 className="text-sm font-medium text-gray-900 mb-2">Notes</h3>
            <p className="text-xs text-gray-700 bg-gray-50 p-3 rounded">{purchaseOrder.notes}</p>
          </div>
        )}
      </div>
      
      {/* Line Items */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Line Items</h2>
        </div>
        
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-xs font-medium text-gray-900 py-3 px-6">Product</TableHead>
                <TableHead className="text-xs font-medium text-gray-900 py-3 px-6">SKU</TableHead>
                <TableHead className="text-xs font-medium text-gray-900 py-3 px-6 text-center">Ordered</TableHead>
                <TableHead className="text-xs font-medium text-gray-900 py-3 px-6 text-center">Received</TableHead>
                <TableHead className="text-xs font-medium text-gray-900 py-3 px-6 text-center">Remaining</TableHead>
                <TableHead className="text-xs font-medium text-gray-900 py-3 px-6 text-right">Unit Cost</TableHead>
                <TableHead className="text-xs font-medium text-gray-900 py-3 px-6 text-right">Total</TableHead>
                <TableHead className="text-xs font-medium text-gray-900 py-3 px-6 text-center">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {purchaseOrder.items.map((item) => {
                const remaining = item.quantity_ordered - item.quantity_received
                const canReceive = remaining > 0 && purchaseOrder.status !== 'received' && purchaseOrder.status !== 'cancelled'
                
                return (
                  <TableRow key={item.id} className="hover:bg-gray-50">
                    <TableCell className="py-3 px-6 text-xs">
                      <div className="space-y-1">
                        <div className="font-medium">{item.product_name}</div>
                        {item.variant_name && (
                          <div className="text-muted-foreground text-xs">{item.variant_name}</div>
                        )}
                        {item.variant_attributes && (
                          <div className="flex gap-1 flex-wrap">
                            {item.variant_attributes.size && (
                              <Badge variant="secondary" className="text-xs">{item.variant_attributes.size}</Badge>
                            )}
                            {item.variant_attributes.color && (
                              <Badge variant="outline" className="text-xs">{item.variant_attributes.color}</Badge>
                            )}
                            {item.variant_attributes.material && (
                              <Badge variant="outline" className="text-xs">{item.variant_attributes.material}</Badge>
                            )}
                            {item.variant_attributes.style && (
                              <Badge variant="outline" className="text-xs">{item.variant_attributes.style}</Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="py-3 px-6 text-xs">
                      {item.sku || '-'}
                    </TableCell>
                    <TableCell className="py-3 px-6 text-xs text-center">
                      {item.quantity_ordered}
                    </TableCell>
                    <TableCell className="py-3 px-6 text-xs text-center">
                      {item.quantity_received}
                    </TableCell>
                    <TableCell className="py-3 px-6 text-xs text-center">
                      <span className={remaining > 0 ? "text-yellow-600 font-medium" : "text-gray-500"}>
                        {remaining}
                      </span>
                    </TableCell>
                    <TableCell className="py-3 px-6 text-xs text-right">
                      {formatCurrency(item.unit_cost, userCurrency)}
                    </TableCell>
                    <TableCell className="py-3 px-6 text-xs text-right">
                      {formatCurrency(item.subtotal, userCurrency)}
                    </TableCell>
                    <TableCell className="py-3 px-6 text-center">
                      {canReceive ? (
                        <Button 
                          size="sm" 
                          variant="outline" 
                          className="h-7 text-xs"
                          onClick={() => handleReceiveStock(item)}
                        >
                          Receive
                        </Button>
                      ) : (
                        <span className="text-xs text-gray-500">-</span>
                      )}
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </div>
      </div>
      
      {/* Receive Stock Modal */}
      <Dialog open={isReceiveModalOpen} onOpenChange={setIsReceiveModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Receive Stock</DialogTitle>
          </DialogHeader>
          
          {selectedItem && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label className="text-xs font-medium">Product</Label>
                <div className="space-y-1">
                  <div className="text-sm font-medium">{selectedItem.product_name}</div>
                  {selectedItem.variant_name && (
                    <div className="text-muted-foreground text-xs">{selectedItem.variant_name}</div>
                  )}
                  {selectedItem.variant_attributes && (
                    <div className="flex gap-1 flex-wrap">
                      {selectedItem.variant_attributes.size && (
                        <Badge variant="secondary" className="text-xs">{selectedItem.variant_attributes.size}</Badge>
                      )}
                      {selectedItem.variant_attributes.color && (
                        <Badge variant="outline" className="text-xs">{selectedItem.variant_attributes.color}</Badge>
                      )}
                      {selectedItem.variant_attributes.material && (
                        <Badge variant="outline" className="text-xs">{selectedItem.variant_attributes.material}</Badge>
                      )}
                      {selectedItem.variant_attributes.style && (
                        <Badge variant="outline" className="text-xs">{selectedItem.variant_attributes.style}</Badge>
                      )}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-xs font-medium">Ordered</Label>
                  <div className="text-sm">{selectedItem.quantity_ordered}</div>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-xs font-medium">Already Received</Label>
                  <div className="text-sm">{selectedItem.quantity_received}</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label className="text-xs font-medium">Quantity to Receive</Label>
                <Input
                  type="number"
                  min="0"
                  max={selectedItem.quantity_ordered - selectedItem.quantity_received}
                  value={receiveQuantity}
                  onChange={(e) => setReceiveQuantity(Number(e.target.value))}
                  className="text-sm"
                />
                <p className="text-xs text-gray-500">
                  Max: {selectedItem.quantity_ordered - selectedItem.quantity_received}
                </p>
              </div>
              
              <div className="flex justify-end gap-3 pt-4">
                <Button 
                  variant="outline" 
                  onClick={() => setIsReceiveModalOpen(false)}
                  className="h-8 text-xs"
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleSubmitReceive}
                  className="h-8 text-xs"
                >
                  Confirm Receive
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}