'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { Plus } from 'lucide-react'
import { PurchaseOrderFilterBar } from '@/components/products/purchase-orders/PurchaseOrderFilterBar'
import { PurchaseOrderTable } from '@/components/products/purchase-orders/PurchaseOrderTable'
import { 
  getPurchaseOrders, 
  deletePurchaseOrder, 
  getSuppliers 
} from '@/components/products/purchase-orders/purchase-order-service'
import { useAuth } from '@/contexts/auth-context'
import { 
  PurchaseOrder, 
  PurchaseOrderFilters, 
  INITIAL_PURCHASE_ORDER_FILTERS 
} from '@/components/products/purchase-orders/types'
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog'
import { PurchaseOrderModal } from '@/components/products/modals/PurchaseOrderModal'
import { getSupabaseClient, getProductsForUser, getAllProductsForUser } from '@/lib/supabase'
import { useRouter } from 'next/navigation'

export default function PurchaseOrdersPage() {
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [suppliers, setSuppliers] = useState<string[]>([])
  const [filters, setFilters] = useState<PurchaseOrderFilters>(INITIAL_PURCHASE_ORDER_FILTERS)
  const [loading, setLoading] = useState(true)
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const { user } = useAuth()
  const { toast } = useToast()
  const router = useRouter()
  
  // Fetch purchase orders
  const fetchPurchaseOrders = async () => {
    if (!user) return
    
    setLoading(true)
    try {
      // Convert date objects to strings for the API
      const apiFilters = {
        ...filters,
        dateRange: {
          from: filters.dateRange.from ? filters.dateRange.from.toISOString() : undefined,
          to: filters.dateRange.to ? filters.dateRange.to.toISOString() : undefined
        }
      }
      
      const orders = await getPurchaseOrders(user.id, apiFilters)
      setPurchaseOrders(orders)
    } catch (error) {
      console.error('Error fetching purchase orders:', error)
      toast({
        title: "Error",
        description: "Failed to fetch purchase orders. Please try again.",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }
  
  // Fetch products
  const fetchProducts = async () => {
    if (!user) return
    
    try {
      const productData = await getAllProductsForUser(user.id)
      // Process products to match expected format
      const processedProducts = productData?.map(product => ({
        ...product,
        variants: product.product_variants || [],
        category_name: product.categories?.name || null
      })) || []
      
      setProducts(processedProducts)
      
      // Extract suppliers from products and update the suppliers list
      const productSuppliers = productData?.map(p => p.supplier).filter(Boolean) || [];
      setSuppliers(prevSuppliers => {
        const allSuppliers = Array.from(new Set([...prevSuppliers, ...productSuppliers]));
        return allSuppliers;
      });
    } catch (error) {
      console.error('Error fetching products:', error)
      toast({
        title: "Error",
        description: "Failed to fetch products. Please try again.",
        variant: "destructive"
      })
    }
  }
  
  // Fetch suppliers
  const fetchSuppliers = async () => {
    if (!user) return
    
    try {
      const supplierList = await getSuppliers(user.id)
      setSuppliers(supplierList)
    } catch (error) {
      console.error('Error fetching suppliers:', error)
    }
  }
  
  // Handle delete
  const handleDelete = async (id: string) => {
    if (!user) return
    
    try {
      await deletePurchaseOrder(id, user.id)
      toast({
        title: "Success",
        description: "Purchase order deleted successfully."
      })
      fetchPurchaseOrders() // Refresh the list
    } catch (error) {
      console.error('Error deleting purchase order:', error)
      toast({
        title: "Error",
        description: "Failed to delete purchase order. Please try again.",
        variant: "destructive"
      })
    }
  }
  
  // Handle filter changes
  const handleFiltersChange = (newFilters: PurchaseOrderFilters) => {
    setFilters(newFilters)
  }
  
  // Refresh data when filters change
  useEffect(() => {
    fetchPurchaseOrders()
  }, [filters, user])
  
  // Fetch suppliers and products on mount
  useEffect(() => {
    fetchSuppliers()
    fetchProducts()
  }, [user])
  
  // Set up real-time subscription for purchase orders
  useEffect(() => {
    if (!user) return
    
    const supabase = getSupabaseClient()
    
    const purchaseOrdersChannel = supabase
      .channel('purchase_orders_changes')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'purchase_orders',
          filter: `user_id=eq.${user.id}`
        },
        () => {
          fetchPurchaseOrders()
          fetchSuppliers()
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'purchase_orders',
          filter: `user_id=eq.${user.id}`
        },
        () => {
          fetchPurchaseOrders()
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'purchase_orders',
          filter: `user_id=eq.${user.id}`
        },
        () => {
          fetchPurchaseOrders()
        }
      )
      .subscribe()
    
    // Cleanup subscription on unmount
    return () => {
      supabase.removeChannel(purchaseOrdersChannel)
    }
  }, [user])
  
  return (
    <div className="space-y-6">
      {/* Page Header with Title and Actions */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-xl font-bold text-gray-900">Purchase Orders</h1>
          <p className="text-xs text-gray-500 mt-1">
            Create, track, and manage all your orders with suppliers
          </p>
        </div>
        
        <Button 
          className="flex items-center gap-1.5 h-8 px-3 bg-blue-600 hover:bg-blue-700"
          onClick={() => setIsAddModalOpen(true)}
        >
          <Plus className="h-4 w-4" />
          <span>New Purchase Order</span>
        </Button>
      </div>
      
      {/* Filter Bar */}
      <PurchaseOrderFilterBar 
        filters={filters} 
        onFiltersChange={handleFiltersChange}
        suppliers={suppliers}
      />
      
      {/* Purchase Orders Table */}
      <PurchaseOrderTable 
        purchaseOrders={purchaseOrders} 
        onDelete={handleDelete} 
      />
      
      {/* Add Purchase Order Modal */}
      <PurchaseOrderModal 
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        selectedItem={null}
        products={products}
        mode="purchase-orders"
      />
    </div>
  )
}