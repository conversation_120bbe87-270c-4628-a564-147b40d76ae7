'use client'

import React, { useState, useEffect } from 'react'
import { TabNavigation } from '@/components/ui/tab-navigation'
import { useToast } from '@/components/ui/use-toast'
import { getSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { GeneralSettings } from '@/components/settings/general-settings'
import { AccountSettings } from '@/components/settings/account-settings'
import { PreferenceSettings } from '@/components/settings/preference-settings'
import { SecuritySettings } from '@/components/settings/security-settings'
import { BillingSettings } from '@/components/settings/billing-settings'

interface ProfileUpdate {
  type: string;
  url: string | null;
  timestamp: number;
}

export default function SettingsPage() {
  const { user } = useAuth()
  const { userCurrency } = useCurrency()
  const { toast } = useToast()
  const supabase = getSupabaseClient()
  
  // Form states
  const [businessName, setBusinessName] = useState('')
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null)
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [displayName, setDisplayName] = useState('')
  const [jobTitle, setJobTitle] = useState('')
  const [profileLoaded, setProfileLoaded] = useState(false)
  const [avatarVersion, setAvatarVersion] = useState(0)  // For avatar changes only
  
  // Load user profile data
  useEffect(() => {
    if (user) {
      loadUserProfile()

      // Check for stored profile updates on initial load
      try {
        const lastUpdate = localStorage.getItem('lastProfileUpdate')
        if (lastUpdate) {
          const updateData: ProfileUpdate = JSON.parse(lastUpdate)
          if (updateData.type === 'avatar') {
            setAvatarUrl(updateData.url)
            setAvatarVersion(prev => prev + 1)
          }
        }
      } catch (error) {
        console.error('Error parsing stored profile update:', error)
      }
    }
  }, [user])

  // Listen for profile updates - but only update avatarVersion
  useEffect(() => {
    const handleProfileUpdate = (event: Event) => {
      if (event instanceof CustomEvent) {
        const updateData = event.detail;
        console.log('Settings page received profile update:', updateData);
        
        if (updateData && updateData.type === 'avatar') {
          setAvatarUrl(updateData.url)
          setAvatarVersion(prev => prev + 1) // Only increment avatar version, not full profile
        }
      }
    }

    window.addEventListener('profileUpdated', handleProfileUpdate)
    
    return () => {
      window.removeEventListener('profileUpdated', handleProfileUpdate)
    }
  }, [])

  // Also refresh profile on visibility change (when returning to the tab)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        loadUserProfile()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [])

  const loadUserProfile = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('business_name, currency, avatar_url, first_name, last_name, display_name, job_title')
        .eq('id', user?.id)
        .single()

      if (error) {
        console.error('Error loading profile:', error)
        return
      }

      setBusinessName(data?.business_name || '')
      setAvatarUrl(data?.avatar_url || null)
      setFirstName(data?.first_name || '')
      setLastName(data?.last_name || '')
      setDisplayName(data?.display_name || '')
      setJobTitle(data?.job_title || '')
      setProfileLoaded(true)
    } catch (error) {
      console.error('Error loading profile:', error)
    }
  }

  // Handle personal info update without causing a re-render of the entire profile
  const handlePersonalInfoUpdate = (info: {
    firstName: string
    lastName: string
    displayName: string
    jobTitle: string
  }) => {
    setFirstName(info.firstName)
    setLastName(info.lastName)
    setDisplayName(info.displayName)
    setJobTitle(info.jobTitle)
  }

  // Define tabs configuration - only the avatar component gets a key that changes
  const tabs = [
    {
      id: 'general',
      label: 'General',
      content: (
        <GeneralSettings
          initialBusinessName={businessName}
          initialCurrency={userCurrency}
          onBusinessNameUpdate={setBusinessName}
          onCurrencyUpdate={() => {}}
        />
      )
    },
    {
      id: 'account',
      label: 'Account',
      content: profileLoaded ? (
        <AccountSettings
          key={`account-avatar-${avatarVersion}`} // Only re-render when avatar changes
          initialAvatarUrl={avatarUrl}
          initialFirstName={firstName}
          initialLastName={lastName}
          initialDisplayName={displayName}
          initialJobTitle={jobTitle}
          initialBusinessName={businessName}
          onAvatarUpdate={setAvatarUrl}
          onPersonalInfoUpdate={handlePersonalInfoUpdate}
        />
      ) : (
        <div className="p-8 text-center text-gray-500">Loading profile data...</div>
      )
    },
    {
      id: 'preferences',
      label: 'Preferences',
      content: <PreferenceSettings />
    },
    {
      id: 'security',
      label: 'Security',
      content: <SecuritySettings />
    },
    {
      id: 'billing',
      label: 'Billing',
      content: <BillingSettings />
    }
  ]

  return (
    <div className="space-y-4">
      <TabNavigation 
        tabs={tabs}
        defaultTab="general"
        enableRouting={true}
        basePath="/dashboard/settings"
      />
    </div>
  )
}