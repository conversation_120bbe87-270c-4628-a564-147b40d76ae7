'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  CalendarDays, 
  TrendingUp, 
  TrendingDown, 
  BarChart3,
  Download,
  Calendar,
  Filter,
  FileSpreadsheet,
  FileText,
  ChevronDown
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts'
import { type ExpenseRow } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { 
  type PeriodType,
  getTrendChartTitle,
  getCategoryBreakdownTitle
} from '@/lib/period-calculations'
import { 
  processAnalyticsData,
  getPrimaryData,
  generateQuickInsights,
  type AnalyticsData,
  type PeriodData,
  type CategoryData
} from '@/lib/analytics-processor'

// Period selection options
const PERIOD_OPTIONS = [
  { value: 'month' as const, label: 'This Month', icon: CalendarDays },
  { value: 'quarter' as const, label: 'This Quarter', icon: BarChart3 },
  { value: 'year' as const, label: 'This Year', icon: Calendar },
]

interface ExpenseAnalyticsProps {
  expenses?: ExpenseRow[]
  onPeriodChange?: (period: PeriodType) => void
  isCollapsible?: boolean
}

export function ExpenseAnalytics({ 
  expenses = [], 
  onPeriodChange,
  isCollapsible = true 
}: ExpenseAnalyticsProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<PeriodType>('month')
  const [isExpanded, setIsExpanded] = useState(true)
  const [isExporting, setIsExporting] = useState(false)
  
  const { user } = useAuth()
  const { formatCurrency } = useCurrency()
  const { toast } = useToast()

  // Process analytics data using modular utilities
  const analyticsData = useMemo(() => {
    return processAnalyticsData(expenses, selectedPeriod)
  }, [expenses, selectedPeriod])

  // Get primary data for the selected period
  const primaryData = useMemo(() => {
    return getPrimaryData(analyticsData, selectedPeriod)
  }, [analyticsData, selectedPeriod])

  // Generate contextual insights
  const quickInsights = useMemo(() => {
    return generateQuickInsights(analyticsData, selectedPeriod)
  }, [analyticsData, selectedPeriod])

  const handlePeriodChange = (period: PeriodType) => {
    setSelectedPeriod(period)
    onPeriodChange?.(period)
  }

  // Export functions for analytics data
  const exportAnalyticsToCSV = useCallback(() => {
    if (!analyticsData) return
    
    setIsExporting(true)
    
    try {
      const currentDate = new Date().toISOString().split('T')[0]
      const periodLabel = selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)
      
      // Create comprehensive analytics CSV
      const lines = [
        '# Expense Analytics Report',
        `# Generated on: ${new Date().toISOString()}`,
        `# Period Focus: ${periodLabel}`,
        `# Primary Period: ${primaryData?.period}`,
        '#',
        '# Summary Data:',
        'Period,Amount,Count,Change %',
        `"${analyticsData.currentPeriod.period}",${analyticsData.currentPeriod.amount.toFixed(2)},${analyticsData.currentPeriod.count},${analyticsData.currentPeriod.change?.toFixed(2) || 'N/A'}`,
        `"${analyticsData.previousPeriod.period}",${analyticsData.previousPeriod.amount.toFixed(2)},${analyticsData.previousPeriod.count},`,
        `"${analyticsData.currentQuarter.period}",${analyticsData.currentQuarter.amount.toFixed(2)},${analyticsData.currentQuarter.count},`,
        `"${analyticsData.currentYear.period}",${analyticsData.currentYear.amount.toFixed(2)},${analyticsData.currentYear.count},`,
        '#',
        '# Category Breakdown:',
        'Category,Amount,Count,Percentage',
        ...analyticsData.topCategories.map(cat => 
          `"${cat.category}",${cat.amount.toFixed(2)},${cat.count},${cat.percentage.toFixed(2)}%`
        ),
        '#',
        `# Trend Data (${getTrendChartTitle(selectedPeriod)}):`,
        'Period,Amount,Count',
        ...analyticsData.trendData.map(trend => 
          `"${trend.period}",${trend.amount.toFixed(2)},${trend.count}`
        )
      ]
      
      const csvContent = lines.join('\n')
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `expense-analytics-${periodLabel.toLowerCase()}-${currentDate}.csv`
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast({
        title: "Analytics Export Complete",
        description: `${periodLabel} analytics report has been downloaded as CSV.`,
      })
    } catch (error) {
      console.error('Analytics CSV export error:', error)
      toast({
        title: "Export Failed",
        description: "Failed to export analytics. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }, [analyticsData, selectedPeriod, primaryData, toast])
  
  const exportAnalyticsToPDF = useCallback(() => {
    if (!analyticsData) return
    
    setIsExporting(true)
    
    try {
      // Dynamic import to avoid SSR issues
      import('jspdf').then(({ default: jsPDF }) => {
        import('jspdf-autotable').then(() => {
          try {
            const doc = new jsPDF()
            const currentDate = new Date().toLocaleDateString()
            const periodLabel = selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)
            
            // Set document properties
            doc.setProperties({
              title: `Expense Analytics Report - ${periodLabel}`,
              subject: 'Business Expense Analytics',
              creator: 'ONKO Expense Management'
            })
            
            // Add header
            doc.setFontSize(20)
            doc.text('Expense Analytics Report', 20, 25)
            
            doc.setFontSize(12)
            doc.text(`Generated on: ${currentDate}`, 20, 35)
            doc.text(`Period Focus: ${periodLabel}`, 20, 45)
            doc.text(`Primary Period: ${primaryData?.period}`, 20, 55)
            
            // Summary section
            doc.setFontSize(14)
            doc.text('Summary', 20, 70)
            
            const summaryData = [
              [analyticsData.currentPeriod.period, formatCurrency(analyticsData.currentPeriod.amount), analyticsData.currentPeriod.count.toString(), analyticsData.currentPeriod.change ? `${analyticsData.currentPeriod.change.toFixed(1)}%` : 'N/A'],
              [analyticsData.previousPeriod.period, formatCurrency(analyticsData.previousPeriod.amount), analyticsData.previousPeriod.count.toString(), '-'],
              [analyticsData.currentQuarter.period, formatCurrency(analyticsData.currentQuarter.amount), analyticsData.currentQuarter.count.toString(), '-'],
              [analyticsData.currentYear.period, formatCurrency(analyticsData.currentYear.amount), analyticsData.currentYear.count.toString(), '-']
            ]
            
            ;(doc as any).autoTable({
              head: [['Period', 'Amount', 'Count', 'Change %']],
              body: summaryData,
              startY: 75,
              styles: { fontSize: 10 },
              headStyles: { fillColor: [59, 130, 246] }
            })
            
            // Category breakdown
            const finalY = (doc as any).lastAutoTable.finalY || 75
            doc.setFontSize(14)
            doc.text('Top Categories', 20, finalY + 20)
            
            const categoryData = analyticsData.topCategories.map(cat => [
              cat.category,
              formatCurrency(cat.amount),
              cat.count.toString(),
              `${cat.percentage.toFixed(1)}%`
            ])
            
            ;(doc as any).autoTable({
              head: [['Category', 'Amount', 'Count', 'Percentage']],
              body: categoryData,
              startY: finalY + 25,
              styles: { fontSize: 10 },
              headStyles: { fillColor: [59, 130, 246] }
            })
            
            // Trend data
            const finalY2 = (doc as any).lastAutoTable.finalY || finalY + 25
            doc.setFontSize(14)
            doc.text(getTrendChartTitle(selectedPeriod), 20, finalY2 + 20)
            
            const trendDataForPDF = analyticsData.trendData.map(trend => [
              trend.period,
              formatCurrency(trend.amount),
              trend.count.toString()
            ])
            
            ;(doc as any).autoTable({
              head: [['Period', 'Amount', 'Count']],
              body: trendDataForPDF,
              startY: finalY2 + 25,
              styles: { fontSize: 10 },
              headStyles: { fillColor: [59, 130, 246] }
            })
            
            // Save PDF
            doc.save(`expense-analytics-${periodLabel.toLowerCase()}-${new Date().toISOString().split('T')[0]}.pdf`)
            
            toast({
              title: "Analytics PDF Export Complete",
              description: `${periodLabel} analytics report has been downloaded as PDF.`,
            })
          } catch (error) {
            console.error('Analytics PDF export error:', error)
            toast({
              title: "PDF Export Failed",
              description: "Failed to generate analytics PDF. Please try again.",
              variant: "destructive",
            })
          } finally {
            setIsExporting(false)
          }
        })
      })
    } catch (error) {
      console.error('PDF library loading error:', error)
      toast({
        title: "PDF Export Failed",
        description: "Failed to load PDF library. Please try again.",
        variant: "destructive",
      })
      setIsExporting(false)
    }
  }, [analyticsData, selectedPeriod, primaryData, formatCurrency, toast])



  if (!analyticsData) {
    const periodLabel = PERIOD_OPTIONS.find(option => option.value === selectedPeriod)?.label || 'Selected Period'
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center space-y-2">
            <div className="text-muted-foreground">Loading {periodLabel.toLowerCase()} analytics...</div>
            <div className="text-xs text-muted-foreground">Processing expense data and calculations</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header with Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Expense Analytics</h2>
          <p className="text-muted-foreground">
            Real-time insights into your business expenses
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Period Selection */}
          <div className="flex items-center space-x-1 bg-muted rounded-lg p-1">
            {PERIOD_OPTIONS.map((option) => {
              const Icon = option.icon
              return (
                <Button
                  key={option.value}
                  variant={selectedPeriod === option.value ? "default" : "ghost"}
                  size="sm"
                  onClick={() => handlePeriodChange(option.value)}
                  className="relative"
                >
                  <Icon className="h-4 w-4 mr-1" />
                  {option.label}
                </Button>
              )
            })}
          </div>
          
          {/* Export Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                size="sm" 
                disabled={isExporting || !analyticsData || expenses.length === 0}
                className="flex items-center space-x-1"
              >
                <Download className="h-4 w-4" />
                <span>Export</span>
                <ChevronDown className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={exportAnalyticsToCSV} disabled={isExporting}>
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Export as CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={exportAnalyticsToPDF} disabled={isExporting}>
                <FileText className="h-4 w-4 mr-2" />
                Export as PDF
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          {/* Collapse Toggle */}
          {isCollapsible && (
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <Filter className={cn("h-4 w-4 transition-transform", isExpanded ? "rotate-180" : "")} />
            </Button>
          )}
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Primary Period Card */}
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-800">
              {primaryData?.period}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800">
              {formatCurrency(primaryData?.amount || 0)}
            </div>
            <div className="flex items-center space-x-2 text-xs">
              <span className="text-primary">
                {primaryData?.count || 0} expenses
              </span>
              {primaryData?.change !== undefined && (
                <div className={cn(
                  "flex items-center space-x-1",
                  primaryData.change >= 0 ? "text-red-600" : "text-green-600"
                )}>
                  {primaryData.change >= 0 ? (
                    <TrendingUp className="h-3 w-3" />
                  ) : (
                    <TrendingDown className="h-3 w-3" />
                  )}
                  <span>{Math.abs(primaryData.change).toFixed(1)}%</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Previous Period Comparison */}
        <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
            <CardTitle className="text-sm font-medium">{analyticsData.previousPeriod.period}</CardTitle>
            <div className="h-6 w-6 rounded-md bg-blue-50 flex items-center justify-center">
              <Calendar className="h-3 w-3 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="p-6 pt-0">
            <div className="text-2xl font-bold">
              {formatCurrency(analyticsData.previousPeriod.amount)}
            </div>
            <p className="text-xs text-muted-foreground">
              {analyticsData.previousPeriod.count} expenses
            </p>
          </CardContent>
        </Card>

        {/* Current Quarter */}
        <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
            <CardTitle className="text-sm font-medium">This Quarter</CardTitle>
            <div className="h-6 w-6 rounded-md bg-blue-50 flex items-center justify-center">
              <BarChart3 className="h-3 w-3 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="p-6 pt-0">
            <div className="text-2xl font-bold">
              {formatCurrency(analyticsData.currentQuarter.amount)}
            </div>
            <p className="text-xs text-muted-foreground">
              {analyticsData.currentQuarter.count} expenses
            </p>
          </CardContent>
        </Card>

        {/* Current Year */}
        <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
            <CardTitle className="text-sm font-medium">This Year</CardTitle>
            <div className="h-6 w-6 rounded-md bg-blue-50 flex items-center justify-center">
              <CalendarDays className="h-3 w-3 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="p-6 pt-0">
            <div className="text-2xl font-bold">
              {formatCurrency(analyticsData.currentYear.amount)}
            </div>
            <p className="text-xs text-muted-foreground">
              {analyticsData.currentYear.count} expenses
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Expandable Detailed Analytics */}
      {isExpanded && (
        <div className="space-y-4">
          {/* Category Breakdown with Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{getCategoryBreakdownTitle(selectedPeriod)}</CardTitle>
              <CardDescription>
                Visual breakdown of spending by category
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analyticsData.topCategories.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <div>No expenses recorded for {primaryData?.period.toLowerCase()}</div>
                  <div className="text-xs mt-1">Add some expenses to see category breakdown</div>
                </div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Pie Chart */}
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={analyticsData.topCategories}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={100}
                          dataKey="amount"
                          nameKey="category"
                        >
                          {analyticsData.topCategories.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip 
                          content={({ active, payload }) => {
                            if (active && payload && payload.length) {
                              const data = payload[0].payload
                              return (
                                <div className="bg-background border rounded-lg shadow-md p-3">
                                  <p className="font-medium">{data.category}</p>
                                  <p className="text-primary">
                                    {formatCurrency(data.amount)} ({data.percentage.toFixed(1)}%)
                                  </p>
                                  <p className="text-muted-foreground text-sm">
                                    {data.count} expenses
                                  </p>
                                </div>
                              )
                            }
                            return null
                          }}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  
                  {/* Category List */}
                  <div className="space-y-3">
                    {analyticsData.topCategories.map((category, index) => (
                      <div key={category.category} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: category.color }}
                          />
                          <span className="font-medium">{category.category}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold">{formatCurrency(category.amount)}</div>
                          <div className="text-xs text-muted-foreground">
                            {category.count} expenses ({category.percentage.toFixed(1)}%)
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Charts Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Interactive Trend Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{getTrendChartTitle(selectedPeriod)}</CardTitle>
                <CardDescription>
                  Historical expense patterns and trends
                </CardDescription>
              </CardHeader>
              <CardContent>
                {analyticsData.trendData.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <div>No trend data available</div>
                    <div className="text-xs mt-1">Add expenses across multiple periods to see trends</div>
                  </div>
                ) : (
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={analyticsData.trendData}>
                        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                        <XAxis 
                          dataKey="period" 
                          fontSize={12}
                          tickLine={false}
                          axisLine={false}
                        />
                        <YAxis
                          fontSize={12}
                          tickLine={false}
                          axisLine={false}
                          tickFormatter={(value) => `$${value}`}
                        />
                        <Tooltip 
                          content={({ active, payload, label }) => {
                            if (active && payload && payload.length) {
                              return (
                                <div className="bg-background border rounded-lg shadow-md p-3">
                                  <p className="font-medium">{label}</p>
                                  <p className="text-primary">
                                    Amount: {formatCurrency(payload[0].value as number)}
                                  </p>
                                  <p className="text-muted-foreground text-sm">
                                    {payload[0].payload.count} expenses
                                  </p>
                                </div>
                              )
                            }
                            return null
                          }}
                        />
                        <Line 
                          type="monotone" 
                          dataKey="amount" 
                          stroke="#3B82F6" 
                          strokeWidth={2}
                          dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                          activeDot={{ r: 6, stroke: '#3B82F6', strokeWidth: 2 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* Quick Insights Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Quick Insights - {primaryData?.period}</CardTitle>
                <CardDescription>
                  Key statistics and trends for selected period
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {quickInsights.map((insight, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">{insight.label}</span>
                      <span className={cn(
                        "font-medium",
                        insight.trend === 'up' && "text-red-600",
                        insight.trend === 'down' && "text-green-600",
                        insight.trend === 'neutral' && "text-muted-foreground"
                      )}>
                        {insight.value}
                      </span>
                    </div>
                  ))}
                  {quickInsights.length === 0 && (
                    <div className="text-center py-4 text-muted-foreground">
                      <div>No insights available for {primaryData?.period.toLowerCase()}</div>
                      <div className="text-xs mt-1">Add expenses to generate insights</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  )
}