'use client'

import { useState, useEffect } from 'react'
import { Button, type ButtonProps } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ExpenseAnalytics } from '@/components/expense-analytics'
import { createSupabaseClient, type ExpenseRow } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useToast } from '@/components/ui/use-toast'
import { usePersistedState } from '@/lib/use-persisted-state'
import { Download, FileSpreadsheet, FileText, Mail } from 'lucide-react'
import { useCurrency } from '@/lib/currency'

interface AnalyticsTabProps {
  isMobile?: boolean
  className?: string
  contextualFilters?: any // ExpenseFilters type
  filteredExpenseIds?: string[]
}

export function AnalyticsTab({ 
  isMobile = false, 
  className,
  contextualFilters,
  filteredExpenseIds = []
}: AnalyticsTabProps) {
  const [expenses, setExpenses] = useState<ExpenseRow[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = usePersistedState<'monthly' | 'quarterly' | 'yearly'>(
    'analytics-period', 
    'monthly'
  )
  const [isExporting, setIsExporting] = useState(false)
  
  const { user } = useAuth()
  const { toast } = useToast()
  const { formatCurrency } = useCurrency()
  const supabase = createSupabaseClient()

  // Load expenses on component mount
  useEffect(() => {
    if (user) {
      loadExpenses()
    }
  }, [user])

  const loadExpenses = async () => {
    if (!user?.id) return
    
    setIsLoading(true)
    try {
      const { data, error } = await supabase
        .from('expenses')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error loading expenses:', error)
        toast({
          title: "Error Loading Expenses",
          description: `Failed to load expenses: ${error.message}`,
          variant: "destructive",
        })
      } else {
        const allExpenses = data || []
        // Apply contextual filtering if filteredExpenseIds are provided
        const contextualExpenses = filteredExpenseIds.length > 0 
          ? allExpenses.filter(expense => filteredExpenseIds.includes(expense.id))
          : allExpenses
        
        setExpenses(contextualExpenses)
      }
    } catch (error) {
      console.error('Error loading expenses:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to load expenses. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Export functionality
  const exportToCSV = async () => {
    setIsExporting(true)
    try {
      const csvContent = generateCSVContent(expenses)
      downloadFile(csvContent, `expense-analytics-${selectedPeriod}-${new Date().toISOString().split('T')[0]}.csv`, 'text/csv')
      
      toast({
        title: "Export Successful",
        description: "Analytics data exported to CSV successfully.",
      })
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export analytics data.",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  const exportToPDF = async () => {
    setIsExporting(true)
    try {
      // For now, show a toast. PDF export would require a library like jsPDF
      toast({
        title: "PDF Export",
        description: "PDF export functionality will be implemented in a future update.",
      })
    } finally {
      setIsExporting(false)
    }
  }

  const emailReport = async () => {
    setIsExporting(true)
    try {
      // For now, show a toast. Email functionality would require backend integration
      toast({
        title: "Email Report",
        description: "Email report functionality will be implemented in a future update.",
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Generate CSV content
  const generateCSVContent = (data: ExpenseRow[]) => {
    const headers = ['Date', 'Description', 'Category', 'Amount', 'Payment Method', 'Vendor']
    const csvRows = [headers.join(',')]
    
    data.forEach(expense => {
      const row = [
        expense.expense_date || expense.created_at.split('T')[0],
        `"${expense.description || ''}"`,
        expense.category || '',
        expense.amount.toString(),
        expense.payment_method || '',
        `"${expense.vendor || ''}"`
      ]
      csvRows.push(row.join(','))
    })
    
    return csvRows.join('\n')
  }

  // Download file utility
  const downloadFile = (content: string, filename: string, mimeType: string) => {
    const blob = new Blob([content], { type: mimeType })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Analytics Content */}
        {isLoading ? (
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <div className="text-center space-y-2">
                <div className="text-muted-foreground">Loading expense data...</div>
                <div className="text-xs text-muted-foreground">Preparing analytics dashboard</div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <ExpenseAnalytics 
            expenses={expenses}
            isCollapsible={false} // Always expanded in dedicated tab
          />
        )}
      </div>
    </div>
  )
}