'use client'

import { useState, useEffect, useMemo, useRef } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useSessionState } from '@/lib/use-persisted-state'
import { 
  Search, 
  Filter, 
  X, 
  SlidersHorizontal,
  RotateCcw
} from 'lucide-react'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Label } from '@/components/ui/label'
import { DatePicker } from '@/components/ui/date-picker'
import { type ExpenseRow } from '@/lib/supabase'
import { useCurrency } from '@/lib/currency'
import { cn } from '@/lib/utils'

// Filter state interface (reusing from ExpenseFilters)
export interface ExpenseFilters {
  searchQuery: string
  categories: string[]
  paymentMethods: string[]
  dateRange: {
    start: Date | null
    end: Date | null
  }
  amountRange: {
    min: number | null
    max: number | null
  }
}

// Initial filter state
export const INITIAL_FILTERS: ExpenseFilters = {
  searchQuery: '',
  categories: [],
  paymentMethods: [],
  dateRange: {
    start: null,
    end: null
  },
  amountRange: {
    min: null,
    max: null
  }
}

interface ExpenseTableHeaderProps {
  expenses: ExpenseRow[]
  filters: ExpenseFilters
  onFiltersChange: (filters: ExpenseFilters) => void
  onFilteredExpensesChange: (filteredExpenses: ExpenseRow[]) => void
  isMobile?: boolean
  className?: string
  enableFilterUrlSync?: boolean // Enable URL sync for filters
}

export function ExpenseTableHeader({
  expenses,
  filters,
  onFiltersChange,
  onFilteredExpensesChange,
  isMobile = false,
  className,
  enableFilterUrlSync = false
}: ExpenseTableHeaderProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [searchDebounce, setSearchDebounce] = useState(filters.searchQuery)
  const [isFilterSheetOpen, setIsFilterSheetOpen] = useSessionState('expense-filter-sheet-open', false)
  const [isExpanded, setIsExpanded] = useState(false)
  
  const { formatCurrency } = useCurrency()

  // Sync filter changes with URL parameters (optional deep linking support)
  useEffect(() => {
    if (!enableFilterUrlSync) return
    
    const currentUrl = new URL(window.location.href)
    const hasActiveFilters = 
      filters.searchQuery.trim() || 
      filters.categories.length > 0 || 
      filters.paymentMethods.length > 0 ||
      filters.dateRange.start || 
      filters.dateRange.end ||
      filters.amountRange.min !== null || 
      filters.amountRange.max !== null
    
    if (hasActiveFilters) {
      // Encode filters in URL for deep linking
      if (filters.searchQuery.trim()) {
        currentUrl.searchParams.set('search', filters.searchQuery)
      } else {
        currentUrl.searchParams.delete('search')
      }
      
      if (filters.categories.length > 0) {
        currentUrl.searchParams.set('categories', filters.categories.join(','))
      } else {
        currentUrl.searchParams.delete('categories')
      }
      
      if (filters.paymentMethods.length > 0) {
        currentUrl.searchParams.set('payment_methods', filters.paymentMethods.join(','))
      } else {
        currentUrl.searchParams.delete('payment_methods')
      }
    } else {
      // Clear filter parameters when no filters are active
      currentUrl.searchParams.delete('search')
      currentUrl.searchParams.delete('categories')
      currentUrl.searchParams.delete('payment_methods')
    }
    
    // Update URL without triggering navigation
    if (currentUrl.toString() !== window.location.href) {
      router.replace(currentUrl.toString(), { scroll: false })
    }
  }, [filters, enableFilterUrlSync, router])

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      onFiltersChange({
        ...filters,
        searchQuery: searchDebounce
      })
    }, 300)
    
    return () => clearTimeout(timer)
  }, [searchDebounce, filters, onFiltersChange])

  // Extract unique categories and payment methods from expenses
  const availableOptions = useMemo(() => {
    const categories = new Set<string>()
    const paymentMethods = new Set<string>()
    
    expenses.forEach(expense => {
      if (expense.category) categories.add(expense.category)
      if (expense.payment_method) paymentMethods.add(expense.payment_method)
    })
    
    return {
      categories: Array.from(categories).map(cat => ({ value: cat, label: cat })),
      paymentMethods: Array.from(paymentMethods).map(pm => ({ value: pm, label: pm }))
    }
  }, [expenses])

  // Filter expenses based on current filters
  const filteredExpenses = useMemo(() => {
    let result = expenses

    // Text search
    if (filters.searchQuery.trim()) {
      const query = filters.searchQuery.toLowerCase()
      result = result.filter(expense => 
        expense.description?.toLowerCase().includes(query) ||
        expense.vendor?.toLowerCase().includes(query) ||
        expense.category?.toLowerCase().includes(query) ||
        expense.expense_id?.toLowerCase().includes(query)
      )
    }

    // Category filter
    if (filters.categories.length > 0) {
      result = result.filter(expense => 
        expense.category && filters.categories.includes(expense.category)
      )
    }

    // Payment method filter
    if (filters.paymentMethods.length > 0) {
      result = result.filter(expense => 
        expense.payment_method && filters.paymentMethods.includes(expense.payment_method)
      )
    }

    // Date range filter
    if (filters.dateRange.start || filters.dateRange.end) {
      result = result.filter(expense => {
        const expenseDate = new Date(expense.expense_date || expense.created_at)
        
        if (filters.dateRange.start && expenseDate < filters.dateRange.start) {
          return false
        }
        
        if (filters.dateRange.end && expenseDate > filters.dateRange.end) {
          return false
        }
        
        return true
      })
    }

    // Amount range filter
    if (filters.amountRange.min !== null || filters.amountRange.max !== null) {
      result = result.filter(expense => {
        if (filters.amountRange.min !== null && expense.amount < filters.amountRange.min) {
          return false
        }
        
        if (filters.amountRange.max !== null && expense.amount > filters.amountRange.max) {
          return false
        }
        
        return true
      })
    }

    return result
  }, [expenses, filters])

  // Use ref to store the latest callback to avoid dependency issues
  const onFilteredExpensesChangeRef = useRef(onFilteredExpensesChange)
  
  // Update ref when callback changes
  useEffect(() => {
    onFilteredExpensesChangeRef.current = onFilteredExpensesChange
  }, [onFilteredExpensesChange])

  // Update filtered expenses when they change (using ref to avoid infinite loops)
  useEffect(() => {
    onFilteredExpensesChangeRef.current(filteredExpenses)
  }, [filteredExpenses])

  // Count active filters
  const activeFilterCount = useMemo(() => {
    let count = 0
    if (filters.searchQuery.trim()) count++
    if (filters.categories.length > 0) count += filters.categories.length
    if (filters.paymentMethods.length > 0) count += filters.paymentMethods.length
    if (filters.dateRange.start || filters.dateRange.end) count++
    if (filters.amountRange.min !== null || filters.amountRange.max !== null) count++
    return count
  }, [filters])

  // Clear all filters
  const clearAllFilters = () => {
    setSearchDebounce('')
    onFiltersChange(INITIAL_FILTERS)
  }

  // Handle category selection (multi-select)
  const handleCategoryToggle = (category: string) => {
    const newCategories = filters.categories.includes(category)
      ? filters.categories.filter(c => c !== category)
      : [...filters.categories, category]
    
    onFiltersChange({
      ...filters,
      categories: newCategories
    })
  }

  // Handle payment method selection (multi-select)
  const handlePaymentMethodToggle = (paymentMethod: string) => {
    const newPaymentMethods = filters.paymentMethods.includes(paymentMethod)
      ? filters.paymentMethods.filter(pm => pm !== paymentMethod)
      : [...filters.paymentMethods, paymentMethod]
    
    onFiltersChange({
      ...filters,
      paymentMethods: newPaymentMethods
    })
  }

  // Generate active filter badges
  const getActiveFilterBadges = () => {
    const badges = []
    
    if (filters.searchQuery.trim()) {
      badges.push(
        <Badge key="search" variant="secondary" className="text-xs">
          Search: "{filters.searchQuery}"
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3 w-3 p-0"
            onClick={() => {
              setSearchDebounce('')
              onFiltersChange({ ...filters, searchQuery: '' })
            }}
          >
            <X className="h-2 w-2" />
          </Button>
        </Badge>
      )
    }
    
    filters.categories.forEach(category => {
      badges.push(
        <Badge key={`cat-${category}`} variant="secondary" className="text-xs">
          {category}
          <Button 
            variant="ghost" 
            size="sm" 
            className="ml-1 h-3 w-3 p-0"
            onClick={() => handleCategoryToggle(category)}
          >
            <X className="h-2 w-2" />
          </Button>
        </Badge>
      )
    })
    
    return badges
  }

  const renderAdvancedFilters = () => (
    <>
      {/* Categories */}
      <div className="space-y-2">
        <Label className="text-xs font-medium text-foreground">Categories</Label>
        <div className="border border-border rounded-md bg-background/50 shadow-xs">
          <div className="max-h-32 overflow-y-auto p-2 space-y-1">
            {availableOptions.categories.length > 0 ? (
              availableOptions.categories.map(category => (
                <label
                  key={category.value}
                  className={cn(
                    "flex items-center space-x-2 p-1.5 rounded transition-all cursor-pointer group",
                    "hover:bg-accent/50 hover:text-accent-foreground text-xs"
                  )}
                >
                  <input
                    type="checkbox"
                    checked={filters.categories.includes(category.value)}
                    onChange={() => handleCategoryToggle(category.value)}
                    className="h-3.5 w-3.5 rounded border-border text-primary focus:ring-primary/20 focus:ring-1"
                  />
                  <span className="font-medium">{category.label}</span>
                </label>
              ))
            ) : (
              <div className="text-center py-3 text-muted-foreground">
                <p className="text-xs">No categories available</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Payment Methods */}
      <div className="space-y-2">
        <Label className="text-xs font-medium text-foreground">Payment Methods</Label>
        <div className="border border-border rounded-md bg-background/50 shadow-xs">
          <div className="max-h-32 overflow-y-auto p-2 space-y-1">
            {availableOptions.paymentMethods.length > 0 ? (
              availableOptions.paymentMethods.map(method => (
                <label
                  key={method.value}
                  className={cn(
                    "flex items-center space-x-2 p-1.5 rounded transition-all cursor-pointer group",
                    "hover:bg-accent/50 hover:text-accent-foreground text-xs"
                  )}
                >
                  <input
                    type="checkbox"
                    checked={filters.paymentMethods.includes(method.value)}
                    onChange={() => handlePaymentMethodToggle(method.value)}
                    className="h-3.5 w-3.5 rounded border-border text-primary focus:ring-primary/20 focus:ring-1"
                  />
                  <span className="font-medium">{method.label}</span>
                </label>
              ))
            ) : (
              <div className="text-center py-3 text-muted-foreground">
                <p className="text-xs">No payment methods available</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Date Range */}
      <div className="space-y-2">
        <Label className="text-xs font-medium text-foreground">Date Range</Label>
        <div className="space-y-2 p-2 border border-border rounded-md bg-background/50 shadow-xs">
          <div className="flex space-x-2">
            <div className="flex-1">
              <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">From</Label>
              <DatePicker
                date={filters.dateRange.start || undefined}
                onDateChange={(date) => onFiltersChange({
                  ...filters,
                  dateRange: { ...filters.dateRange, start: date || null }
                })}
                placeholder="Select start date"
                className="w-full h-8 text-xs px-2 py-1"
              />
            </div>
            <div className="flex-1">
              <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">To</Label>
              <DatePicker
                date={filters.dateRange.end || undefined}
                onDateChange={(date) => onFiltersChange({
                  ...filters,
                  dateRange: { ...filters.dateRange, end: date || null }
                })}
                placeholder="Select end date"
                className="w-full h-8 text-xs px-2 py-1"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Amount Range */}
      <div className="space-y-2">
        <Label className="text-xs font-medium text-foreground">Amount Range</Label>
        <div className="space-y-2 p-2 border border-border rounded-md bg-background/50 shadow-xs">
          <div className="flex space-x-2">
            <div className="flex-1">
              <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide sr-only">Minimum</Label>
              <Input
                type="number"
                step="0.01"
                placeholder="Min"
                value={filters.amountRange.min || ''}
                onChange={(e) => onFiltersChange({
                  ...filters,
                  amountRange: { 
                    ...filters.amountRange, 
                    min: e.target.value ? parseFloat(e.target.value) : null 
                  }
                })}
                className="w-full h-8 text-xs px-2 py-1"
              />
            </div>
            <div className="flex-1">
              <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide sr-only">Maximum</Label>
              <Input
                type="number"
                step="0.01"
                placeholder="Max"
                value={filters.amountRange.max || ''}
                onChange={(e) => onFiltersChange({
                  ...filters,
                  amountRange: { 
                    ...filters.amountRange, 
                    max: e.target.value ? parseFloat(e.target.value) : null 
                  }
                })}
                className="w-full h-8 text-xs px-2 py-1"
              />
            </div>
          </div>
        </div>
      </div>
    </>
  )

  return (
    <div className={cn("space-y-4", className)}>
      {/* Search and Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-3">
        {/* Search Input */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-3.5 w-3.5" />
          <Input
            placeholder="Search expenses..."
            value={searchDebounce}
            onChange={(e) => setSearchDebounce(e.target.value)}
            className={cn(
              "pl-9 h-8 text-xs",
              isMobile && "text-sm min-h-[40px] touch-manipulation"
            )}
            autoComplete="off"
            spellCheck="false"
            autoCapitalize="none"
            autoCorrect="off"
          />
        </div>

        {/* Filter Controls */}
        <div className="flex gap-2">
          {/* Advanced Filters Trigger */}
          {isMobile ? (
            <Sheet open={isFilterSheetOpen} onOpenChange={setIsFilterSheetOpen}>
              <SheetTrigger asChild>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="relative touch-manipulation min-h-[44px] px-4"
                >
                  <SlidersHorizontal className="h-4 w-4 mr-2" />
                  <span className="font-medium">Filters</span>
                  {activeFilterCount > 0 && (
                    <Badge 
                      variant="destructive" 
                      className="ml-2 h-5 w-5 p-0 text-xs font-semibold"
                    >
                      {activeFilterCount}
                    </Badge>
                  )}
                </Button>
              </SheetTrigger>
              <SheetContent 
                side="bottom" 
                className="h-[90vh] overflow-y-auto bg-background/95 backdrop-blur-sm"
                onOpenAutoFocus={(e) => e.preventDefault()}
              >
                <SheetHeader className="text-left pb-4 border-b">
                  <SheetTitle className="text-sm font-semibold">Filter Expenses</SheetTitle>
                </SheetHeader>
                <div className="py-6 space-y-6">
                  {renderAdvancedFilters()}
                </div>
                <div className="sticky bottom-0 bg-background/95 backdrop-blur-sm pt-4 border-t">
                  <div className="flex gap-3">
                    <Button 
                      onClick={clearAllFilters} 
                      variant="outline" 
                      size="lg"
                      className="flex-1 touch-manipulation min-h-[48px] font-medium"
                      disabled={activeFilterCount === 0}
                    >
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Clear All {activeFilterCount > 0 && `(${activeFilterCount})`}
                    </Button>
                    <Button 
                      onClick={() => setIsFilterSheetOpen(false)}
                      size="lg"
                      className="flex-1 touch-manipulation min-h-[48px] font-medium"
                    >
                      Apply Filters
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          ) : (
            <Button 
              variant="outline" 
              size="sm" 
              className="relative h-8 px-3 text-xs border-gray-300"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <SlidersHorizontal className="h-3.5 w-3.5 mr-2" />
              Filters
              {activeFilterCount > 0 && (
                <span className="ml-1.5 flex items-center justify-center w-4 h-4 rounded-full bg-red-500 text-white text-xs font-medium">
                  {activeFilterCount}
                </span>
              )}
            </Button>
          )}

          {/* Clear All Button (when filters are active) */}
          {activeFilterCount > 0 && !isMobile && (
            <Button 
              onClick={clearAllFilters} 
              variant="ghost" 
              size="sm"
              className="h-8 px-2.5 text-xs hover:bg-destructive hover:text-destructive-foreground transition-colors"
            >
              <X className="h-3.5 w-3.5 mr-1" />
              Clear ({activeFilterCount})
            </Button>
          )}
        </div>
      </div>

      {/* Collapsible Advanced Filters */}
      {!isMobile && isExpanded && (
        <div className="border border-border/50 bg-card/50 backdrop-blur-sm p-6 rounded-lg space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h3 className="text-sm font-semibold tracking-tight">Advanced Filters</h3>
            </div>
            <Button 
              onClick={clearAllFilters} 
              variant="outline" 
              size="sm"
              disabled={activeFilterCount === 0}
              className="h-8 px-3"
            >
              <RotateCcw className="h-3.5 w-3.5 mr-1.5" />
              Clear All
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
            {renderAdvancedFilters()}
          </div>
        </div>
      )}

      {/* Active Filter Badges */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap gap-1">
          {getActiveFilterBadges()}
        </div>
      )}

      {/* Results Count - Hidden to match product table design */}
      {/* <div className="text-sm text-muted-foreground">
        Showing {filteredExpenses.length} of {expenses.length} expenses
        {activeFilterCount > 0 && (
          <span> ({activeFilterCount} filter{activeFilterCount > 1 ? 's' : ''} applied)</span>
        )}
      </div> */}
    </div>
  )
}