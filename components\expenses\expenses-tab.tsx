'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { AddExpenseForm } from '@/components/expenses/add-expense-form'
import { ExpenseTableHeader, type ExpenseFilters as ExpenseFiltersType, INITIAL_FILTERS } from '@/components/expenses/expense-table-header'
import { EnhancedExpenseTable } from '@/components/enhanced-expense-table'
import { createSupabaseClient, type ExpenseRow } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { FileSpreadsheet } from 'lucide-react'

interface ExpensesTabProps {
  isMobile?: boolean
  className?: string
  onFiltersChange?: (filters: ExpenseFiltersType) => void
  onFilteredExpensesChange?: (expenses: ExpenseRow[]) => void
}

export function ExpensesTab({ 
  isMobile = false, 
  className,
  onFiltersChange,
  onFilteredExpensesChange: externalOnFilteredExpensesChange
}: ExpensesTabProps) {
  // Expenses state
  const [expenses, setExpenses] = useState<ExpenseRow[]>([])
  const [isLoadingExpenses, setIsLoadingExpenses] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  
  // Filter state
  const [filters, setFilters] = useState<ExpenseFiltersType>(INITIAL_FILTERS)
  const [filteredExpenses, setFilteredExpenses] = useState<ExpenseRow[]>([])
  
  // Combined filter change handler
  const handleFiltersChange = (newFilters: ExpenseFiltersType) => {
    setFilters(newFilters)
    onFiltersChange?.(newFilters)
  }

  // Combined filtered expenses change handler (memoized to prevent infinite loops)
  const handleFilteredExpensesChange = useCallback((expenses: ExpenseRow[]) => {
    setFilteredExpenses(expenses)
    externalOnFilteredExpensesChange?.(expenses)
  }, [externalOnFilteredExpensesChange])
  
  const { toast } = useToast()
  const { user } = useAuth()
  const { formatCurrency } = useCurrency()
  const supabase = createSupabaseClient()

  // Export to CSV function
  const exportToCSV = () => {
    setIsExporting(true)
    
    try {
      // Create CSV header with metadata
      const metadataLines = [
        '# Expense Report',
        `# Generated on: ${new Date().toISOString()}`,
        `# Total Expenses: ${filteredExpenses.length}`,
        `# Total Amount: ${filteredExpenses.reduce((sum, exp) => sum + exp.amount, 0).toFixed(2)}`,
        `# Filtered from ${expenses.length} total expenses`,
        '#',
        '# Report Data:'
      ];
      
      // CSV header row
      const headers = ['Expense ID', 'Date', 'Description', 'Category', 'Vendor', 'Payment Method', 'Amount', 'Created At'];
      
      // CSV data rows with proper escaping
      const dataRows = filteredExpenses.map(expense => {
        const expenseDate = new Date(expense.expense_date || expense.created_at);
        const createdDate = new Date(expense.created_at);
        
        const row = [
          expense.expense_id || 'N/A',
          expenseDate.toLocaleDateString('en-US'),
          '"' + (expense.description || '').replace(/"/g, '""') + '"', // Escape quotes
          expense.category || '',
          '"' + (expense.vendor || '').replace(/"/g, '""') + '"', // Escape quotes
          expense.payment_method || '',
          expense.amount.toFixed(2),
          createdDate.toISOString()
        ].join(',')
        
        return row;
      })
      
      // Combine all parts
      const csvContent = [
        ...metadataLines,
        headers.join(','),
        ...dataRows
      ].join('\n')

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `expenses-export-${new Date().toISOString().split('T')[0]}.csv`
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toast({
        title: "CSV Export Complete",
        description: `Successfully exported ${filteredExpenses.length} expenses to CSV format.`,
      })
    } catch (error) {
      console.error('CSV export error:', error)
      toast({
        title: "CSV Export Failed",
        description: "Failed to export expenses. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }

  // Load expenses on component mount
  useEffect(() => {
    if (user) {
      loadExpenses()
    }
  }, [user])

  const loadExpenses = async () => {
    if (!user?.id) return
    
    setIsLoadingExpenses(true)
    try {
      const { data, error } = await supabase
        .from('expenses')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error loading expenses:', error)
        toast({
          title: "Error Loading Expenses",
          description: `Failed to load expenses: ${error.message}`,
          variant: "destructive",
        })
      } else {
        setExpenses(data || [])
      }
    } catch (error) {
      console.error('Error loading expenses:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to load expenses. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoadingExpenses(false)
    }
  }

  const handleExpenseAdded = () => {
    loadExpenses()
  }

  const handleExpenseEdit = (expense: ExpenseRow) => {
    // For now, show a toast. In the future, we could open an edit modal
    toast({
      title: "Edit Expense",
      description: "Edit functionality will be implemented in a future update.",
    })
  }

  const handleExpenseDelete = async (expenseIds: string[]) => {
    if (!user?.id) return
    
    try {
      const { error } = await supabase
        .from('expenses')
        .delete()
        .in('id', expenseIds)
        .eq('user_id', user.id)
      
      if (error) {
        toast({
          title: "Error Deleting Expenses",
          description: `Failed to delete expenses: ${error.message}`,
          variant: "destructive",
        })
      } else {
        // Refresh the expenses list
        await loadExpenses()
        
        // Dispatch dashboard refresh event
        window.dispatchEvent(new CustomEvent('dashboardRefresh'))
        
        toast({
          title: "Expenses Deleted",
          description: `Successfully deleted ${expenseIds.length} expense(s).`,
        })
      }
    } catch (error) {
      console.error('Error deleting expenses:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to delete expenses. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleExpenseView = (expense: ExpenseRow) => {
    toast({
      title: `Expense Details - ${expense.expense_id}`,
      description: `${expense.description} | ${expense.category} | ${formatCurrency(expense.amount)}`,
    })
  }

  return (
    <div className={className}>
      <div className="space-y-4">
        {/* Add Expense Form - Collapsible after first use */}
        <AddExpenseForm 
          onExpenseAdded={handleExpenseAdded} 
          isCollapsible={true}
          initiallyExpanded={true}
        />

        {/* Expenses Table with Integrated Filters */}
        {isLoadingExpenses ? (
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <div className="text-muted-foreground">Loading expenses...</div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader className="pb-0">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base font-medium text-gray-900">Expense Records</CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exportToCSV}
                  disabled={isExporting || expenses.length === 0}
                  className="flex items-center space-x-2 h-8 px-3 text-xs border-gray-200"
                >
                  <FileSpreadsheet className="h-3.5 w-3.5 text-gray-600" />
                  <span>Export CSV</span>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {/* Integrated Table Header with Filters - now below the title */}
              <ExpenseTableHeader
                expenses={expenses}
                filters={filters}
                onFiltersChange={handleFiltersChange}
                onFilteredExpensesChange={handleFilteredExpensesChange}
                isMobile={isMobile}
                className="mb-4 mt-4"
                enableFilterUrlSync={true}
              />
              
              {/* Enhanced Expense Table */}
              <EnhancedExpenseTable
                expenses={filteredExpenses}
                onExpenseEdit={handleExpenseEdit}
                onExpenseDelete={handleExpenseDelete}
                onExpenseView={handleExpenseView}
                showBulkActions={false}
                defaultPageSize={isMobile ? 5 : 10}
              />
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}