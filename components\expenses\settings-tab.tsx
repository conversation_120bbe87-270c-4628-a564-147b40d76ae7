'use client'

import { useState, useEffect } from 'react'
import { CategoryManager } from '@/components/category-manager'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button, type ButtonProps } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { usePersistedState } from '@/lib/use-persisted-state'
import { createSupabaseClient, type ExpenseRow } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'

interface SettingsTabProps {
  isMobile?: boolean
  className?: string
}

export function SettingsTab({ isMobile = false, className }: SettingsTabProps) {
  const [expenses, setExpenses] = useState<ExpenseRow[]>([])
  const [isLoading, setIsLoading] = useState(false)
  
  // Settings state with persistence
  const [emailNotifications, setEmailNotifications] = usePersistedState('settings-email-notifications', true)
  const [autoBackup, setAutoBackup] = usePersistedState('settings-auto-backup', false)
  const [defaultCategory, setDefaultCategory] = usePersistedState('settings-default-category', 'General')
  const [defaultPaymentMethod, setDefaultPaymentMethod] = usePersistedState('settings-default-payment', 'Credit Card')
  const [autoFillVendor, setAutoFillVendor] = usePersistedState('settings-auto-fill-vendor', true)
  const [reminderEnabled, setReminderEnabled] = usePersistedState('settings-reminder-enabled', false)
  const [receiptReminder, setReceiptReminder] = usePersistedState('settings-receipt-reminder', true)
  
  const { user } = useAuth()
  const { toast } = useToast()
  const supabase = createSupabaseClient()

  // Load expenses on component mount
  useEffect(() => {
    if (user) {
      loadExpenses()
    }
  }, [user])

  const loadExpenses = async () => {
    if (!user?.id) return
    
    setIsLoading(true)
    try {
      const { data, error } = await supabase
        .from('expenses')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error loading expenses:', error)
        toast({
          title: "Error Loading Expenses",
          description: `Failed to load expenses: ${error.message}`,
          variant: "destructive",
        })
      } else {
        setExpenses(data || [])
      }
    } catch (error) {
      console.error('Error loading expenses:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to load expenses. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCategoryUpdate = (oldCategory: string, newCategory: string) => {
    toast({
      title: "Category Updated",
      description: `Category "${oldCategory}" has been updated to "${newCategory}".`,
    })
    // Reload expenses to reflect changes
    loadExpenses()
  }

  const handleCategoryDelete = (category: string) => {
    toast({
      title: "Category Deleted",
      description: `Category "${category}" has been deleted.`,
    })
    // Reload expenses to reflect changes
    loadExpenses()
  }

  const handleSaveSettings = () => {
    // In a real app, you would save these to a user preferences table
    toast({
      title: "Settings Saved",
      description: "Your expense preferences have been saved successfully.",
    })
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-4 py-3 border-b border-gray-200">
          <h3 className="text-base font-medium text-gray-900">Expense Settings</h3>
        </div>
        <div className="p-4">
          <div className="flex items-center justify-center py-8">
            <div className="text-sm text-gray-500">Loading settings...</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Expense Preferences */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-4 py-3 border-b border-gray-200">
            <h3 className="text-base font-medium text-gray-900">Expense Preferences</h3>
          </div>
          <div className="p-4 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Default Category */}
              <div className="space-y-2">
                <Label htmlFor="default-category" className="text-sm font-medium text-gray-700">Default Category</Label>
                <Select value={defaultCategory} onValueChange={setDefaultCategory}>
                  <SelectTrigger className="text-sm h-9">
                    <SelectValue placeholder="Select default category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="General">General</SelectItem>
                    <SelectItem value="Office">Office Supplies</SelectItem>
                    <SelectItem value="Travel">Travel & Transportation</SelectItem>
                    <SelectItem value="Meals">Meals & Entertainment</SelectItem>
                    <SelectItem value="Software">Software & Subscriptions</SelectItem>
                    <SelectItem value="Marketing">Marketing & Advertising</SelectItem>
                    <SelectItem value="Utilities">Utilities</SelectItem>
                    <SelectItem value="Equipment">Equipment & Hardware</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Default Payment Method */}
              <div className="space-y-2">
                <Label htmlFor="default-payment" className="text-sm font-medium text-gray-700">Default Payment Method</Label>
                <Select value={defaultPaymentMethod} onValueChange={setDefaultPaymentMethod}>
                  <SelectTrigger className="text-sm h-9">
                    <SelectValue placeholder="Select default payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Credit Card">Credit Card</SelectItem>
                    <SelectItem value="Debit Card">Debit Card</SelectItem>
                    <SelectItem value="Cash">Cash</SelectItem>
                    <SelectItem value="Bank Transfer">Bank Transfer</SelectItem>
                    <SelectItem value="PayPal">PayPal</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Notification & Automation Settings */}
            <div className="space-y-4">
              <div className="border-b border-gray-200 pb-2">
                <h4 className="text-sm font-medium text-gray-900">Notifications & Automation</h4>
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-notifications" className="text-sm font-medium text-gray-700">Email Notifications</Label>
                    <p className="text-xs text-gray-500">
                      Receive email notifications for expense reminders and summaries
                    </p>
                  </div>
                  <Switch
                    id="email-notifications"
                    checked={emailNotifications}
                    onCheckedChange={setEmailNotifications}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-backup" className="text-sm font-medium text-gray-700">Auto Backup</Label>
                    <p className="text-xs text-gray-500">
                      Automatically backup your expense data monthly
                    </p>
                  </div>
                  <Switch
                    id="auto-backup"
                    checked={autoBackup}
                    onCheckedChange={setAutoBackup}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-fill-vendor" className="text-sm font-medium text-gray-700">Auto-fill Vendor Names</Label>
                    <p className="text-xs text-gray-500">
                      Automatically suggest vendor names based on previous entries
                    </p>
                  </div>
                  <Switch
                    id="auto-fill-vendor"
                    checked={autoFillVendor}
                    onCheckedChange={setAutoFillVendor}
                  />
                </div>
              </div>
            </div>

            {/* Reminders */}
            <div className="space-y-4">
              <div className="border-b border-gray-200 pb-2">
                <h4 className="text-sm font-medium text-gray-900">Reminders</h4>
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="reminder-enabled" className="text-sm font-medium text-gray-700">Enable Reminders</Label>
                    <p className="text-xs text-gray-500">
                      Get reminders to track expenses regularly
                    </p>
                  </div>
                  <Switch
                    id="reminder-enabled"
                    checked={reminderEnabled}
                    onCheckedChange={setReminderEnabled}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="receipt-reminder" className="text-sm font-medium text-gray-700">Receipt Reminder</Label>
                    <p className="text-xs text-gray-500">
                      Remind to upload receipts for expenses without them
                    </p>
                  </div>
                  <Switch
                    id="receipt-reminder"
                    checked={receiptReminder}
                    onCheckedChange={setReceiptReminder}
                  />
                </div>
              </div>
            </div>

            <div className="pt-2">
              <Button 
                onClick={handleSaveSettings}
                className="h-9 px-3 text-sm"
              >
                Save Preferences
              </Button>
            </div>
          </div>
        </div>

        {/* Category Management */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="px-4 py-3 border-b border-gray-200">
            <h3 className="text-base font-medium text-gray-900">Category Management</h3>
          </div>
          <div className="p-4">
            <CategoryManager 
              expenses={expenses}
              onCategoryUpdate={handleCategoryUpdate}
              onCategoryDelete={handleCategoryDelete}
            />
          </div>
        </div>
      </div>
    </div>
  )
}