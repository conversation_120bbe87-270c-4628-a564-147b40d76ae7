'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { 
  LogOut, 
  User,
  ChevronDown
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from '@/lib/utils'
import Link from 'next/link'
import { getSupabaseClient } from '@/lib/supabase'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { NotificationPanel } from '@/components/ui/notification-panel'

interface HeaderProps {
  className?: string
}

interface ProfileUpdate {
  type: string;
  url?: string | null;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  jobTitle?: string;
  timestamp: number;
}

export function Header({ className }: HeaderProps) {
  const { user, signOut } = useAuth()
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null)
  const [avatarTimestamp, setAvatarTimestamp] = useState<number>(0)
  const [displayName, setDisplayName] = useState<string>('')
  const [lastUpdateTime, setLastUpdateTime] = useState<number>(0)
  const supabase = getSupabaseClient()

  // Load user profile data including avatar
  useEffect(() => {
    if (user) {
      loadUserProfile()

      // Check for stored profile updates
      try {
        const lastUpdate = localStorage.getItem('lastProfileUpdate')
        if (lastUpdate) {
          const updateData: ProfileUpdate = JSON.parse(lastUpdate)
          if (updateData && updateData.timestamp > lastUpdateTime) {
            if (updateData.type === 'avatar') {
              setAvatarUrl(updateData.url || null)
              setAvatarTimestamp(updateData.timestamp)
              setLastUpdateTime(updateData.timestamp)
            }
          }
        }
      } catch (error) {
        console.error('Error parsing stored profile update:', error)
      }
    }
  }, [user])

  // Listen for profile picture updates
  useEffect(() => {
    // Create event listener for profile updates
    const handleProfileUpdate = (event: Event) => {
      if (event instanceof CustomEvent) {
        const updateData = event.detail as ProfileUpdate;
        
        // Only process if this is a newer update than we've seen
        if (updateData && updateData.timestamp > lastUpdateTime) {
          console.log('Header received profile update:', updateData);
          
          if (updateData.type === 'avatar') {
            setAvatarUrl(updateData.url || null)
            setAvatarTimestamp(updateData.timestamp)
            setLastUpdateTime(updateData.timestamp)
          }
          else if (updateData.type === 'personalInfo') {
            // Update display name if available
            if (updateData.displayName) {
              setDisplayName(updateData.displayName)
            } else if (updateData.firstName && updateData.lastName) {
              setDisplayName(`${updateData.firstName} ${updateData.lastName}`)
            } else if (updateData.firstName) {
              setDisplayName(updateData.firstName)
            }
            setLastUpdateTime(updateData.timestamp)
          }
        }
      } else {
        // Legacy support for simple events
        loadUserProfile()
      }
    }

    // Listen for the custom event
    window.addEventListener('profileUpdated', handleProfileUpdate)
    
    return () => {
      window.removeEventListener('profileUpdated', handleProfileUpdate)
    }
  }, [lastUpdateTime])

  const loadUserProfile = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('avatar_url, display_name, first_name, last_name')
        .eq('id', user?.id)
        .single()

      if (error) {
        console.error('Error loading profile:', error)
        return
      }

      setAvatarUrl(data?.avatar_url || null)
      setAvatarTimestamp(new Date().getTime()) // Update timestamp whenever we load from DB
      
      // Set display name with fallbacks
      if (data?.display_name) {
        setDisplayName(data.display_name)
      } else if (data?.first_name && data?.last_name) {
        setDisplayName(`${data.first_name} ${data.last_name}`)
      } else if (data?.first_name) {
        setDisplayName(data.first_name)
      } else if (user?.email) {
        setDisplayName(user.email.split('@')[0])
      }
      
      setLastUpdateTime(new Date().getTime())
    } catch (error) {
      console.error('Error loading profile:', error)
    }
  }

  const handleSignOut = async () => {
    await signOut()
  }

  return (
    <header className={cn(
      "fixed top-0 right-0 left-0 h-14 bg-white border-b border-gray-300 shadow-sm flex items-center justify-end px-4 md:px-6 z-40",
      "md:left-56", // Adjust for sidebar width on desktop
      className
    )}>
      <div className="flex items-center space-x-2">
        {/* Notification Panel */}
        <NotificationPanel />
        
        {/* Subtle Divider */}
        <div className="h-8 w-px bg-gray-200" />
        
        {/* User Profile Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="ghost" 
              size="sm" 
              className="flex items-center gap-2 rounded-full hover:text-gray-900"
            >
              <Avatar className="w-8 h-8">
                {avatarUrl ? (
                  <AvatarImage 
                    src={avatarUrl + '?' + avatarTimestamp} 
                    alt="Profile picture" 
                  />
                ) : (
                  <AvatarFallback className="bg-gray-200">
                    <User className="h-4 w-4 text-gray-600" />
                  </AvatarFallback>
                )}
              </Avatar>
              <span className="text-sm font-medium text-gray-700 hidden sm:inline-block">
                {displayName}
              </span>
              <ChevronDown className="h-4 w-4 text-gray-500" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <div className="px-2 pt-2 pb-2">
              <p className="text-xs font-medium text-gray-500">Signed in as</p>
              <p className="text-sm font-medium text-gray-900 truncate">{user?.email}</p>
            </div>
            <Link href="/dashboard/settings?tab=account" className="block">
              <DropdownMenuItem className="flex items-center cursor-pointer">
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
            </Link>
            <DropdownMenuItem 
              className="flex items-center cursor-pointer text-red-600 focus:text-red-600 focus:bg-red-50"
              onClick={handleSignOut}
            >
              <LogOut className="mr-2 h-4 w-4" />
              <span>Sign out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  )
}