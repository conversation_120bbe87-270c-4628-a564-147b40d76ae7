import React, { useEffect, useState } from 'react'
import { RefreshCw, Info } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useProductForm } from '../../context/ProductFormContext'
import { SectionHeader } from '../shared/SectionHeader'
import { FormField } from '../shared/FormField'
import { createSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

// SKU generation utility
function generateSKU(productName: string): string {
  if (!productName.trim()) return ''

  const namePrefix = productName
    .split(' ')
    .map(word => word.substring(0, 2).toUpperCase())
    .join('')
    .substring(0, 6)

  const date = new Date().toISOString().slice(0, 10).replace(/-/g, '')
  const random = Math.floor(Math.random() * 100).toString().padStart(2, '0')

  return `${namePrefix}-${date}-${random}`
}

function SKUGenerator() {
  const { formData, updateFormData } = useProductForm()

  const handleGenerateSKU = () => {
    const newSKU = generateSKU(formData.name)
    updateFormData('base_sku', newSKU)
  }

  return (
    <Button
      type="button"
      variant="ghost"
      size="sm"
      onClick={handleGenerateSKU}
      className="h-6 px-2 text-xs"
      disabled={!formData.name.trim()}
    >
      <RefreshCw className="h-3 w-3 mr-1" />
      Generate
    </Button>
  )
}

export function ProductDetailsSection() {
  const { formData, updateFormData, errors, categories, setCategories } = useProductForm()
  const { user } = useAuth()
  const [isLoadingCategories, setIsLoadingCategories] = useState(false)

  // Auto-generate SKU when product name changes
  useEffect(() => {
    if (formData.name && !formData.base_sku) {
      const autoSKU = generateSKU(formData.name)
      updateFormData('base_sku', autoSKU)
    }
  }, [formData.name, formData.base_sku, updateFormData])

  // Load categories
  useEffect(() => {
    const loadCategories = async () => {
      if (!user?.id) return

      setIsLoadingCategories(true)
      try {
        const supabase = createSupabaseClient()
        const { data, error } = await supabase
          .from('categories')
          .select('id, name')
          .eq('user_id', user.id)
          .order('name')

        if (error) throw error
        setCategories(data || [])
      } catch (error) {
        console.error('Error loading categories:', error)
      } finally {
        setIsLoadingCategories(false)
      }
    }

    loadCategories()
  }, [user?.id, setCategories])

  return (
    <div className="max-w-4xl">
      <SectionHeader
        title="Product Details"
        description="Essential product information"
      />

      <div className="space-y-4">
        {/* Product Name */}
        <FormField
          label="Product Name"
          required
          error={errors.name}
        >
          <Input
            value={formData.name}
            onChange={(e) => updateFormData('name', e.target.value)}
            className="h-8 text-xs"
          />
        </FormField>

        {/* SKU and Category in the same row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            label={
              <div className="flex items-center gap-1">
                <span>SKU</span>
                <span className="text-red-500">*</span>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-4 w-4 cursor-help -mt-0.5">
                      <Info className="h-3 w-3 text-gray-400" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="max-w-[250px]" side="right" align="start">
                    <div className="space-y-2">
                      <h4 className="font-semibold">SKU Information</h4>
                      <p className="text-xs text-muted-foreground">
                        Click "Generate" to auto-create an SKU or type your own
                      </p>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            }
            required
            error={errors.base_sku}
            action={<SKUGenerator />}
          >
            <Input
              value={formData.base_sku}
              onChange={(e) => updateFormData('base_sku', e.target.value)}
              className="h-8 font-mono text-xs w-full"
            />
          </FormField>

          <FormField
            label="Category"
            error={errors.category_id}
            action={<div className="h-6 w-6" />} // Placeholder to match SKU field action height
          >
            <Select
              value={formData.category_id}
              onValueChange={(value) => updateFormData('category_id', value)}
            >
              <SelectTrigger className="h-8 text-xs w-full">
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                {isLoadingCategories ? (
                  <SelectItem value="loading" disabled>Loading categories...</SelectItem>
                ) : categories.length === 0 ? (
                  <SelectItem value="none" disabled>No categories found</SelectItem>
                ) : (
                  categories.map((category) => (
                    <SelectItem key={category.id} value={category.id} className="text-xs">
                      {category.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </FormField>
        </div>

        {/* Description */}
        <FormField
          label="Description"
          error={errors.description}
        >
          <Textarea
            value={formData.description}
            onChange={(e) => updateFormData('description', e.target.value)}
            rows={3}
            className="resize-none text-xs"
          />
        </FormField>

        {/* Simple Product Attributes */}
        {!formData.has_variants && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Size"
              error={errors.size}
            >
              <Input
                value={formData.size}
                onChange={(e) => updateFormData('size', e.target.value)}
                className="h-8 text-xs"
              />
            </FormField>

            <FormField
              label="Color"
              error={errors.color}
            >
              <Input
                value={formData.color}
                onChange={(e) => updateFormData('color', e.target.value)}
                className="h-8 text-xs"
              />
            </FormField>
          </div>
        )}

        {/* Brand & Supplier */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            label="Brand"
            error={errors.brand}
          >
            <Input
              value={formData.brand}
              onChange={(e) => updateFormData('brand', e.target.value)}
              className="h-8 text-xs"
            />
          </FormField>

          <FormField
            label="Supplier"
            error={errors.supplier}
          >
            <Input
              value={formData.supplier}
              onChange={(e) => updateFormData('supplier', e.target.value)}
              className="h-8 text-xs"
            />
          </FormField>
        </div>

        {/* Barcode */}
        <FormField
          label="Barcode"
          error={errors.barcode}
        >
          <Input
            value={formData.barcode}
            onChange={(e) => updateFormData('barcode', e.target.value)}
            className="h-8 font-mono text-xs"
          />
        </FormField>

        {/* Variable Product Info - Removed as per user request */}
      </div>
    </div>
  )
}