import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AttributeManagementSection } from '../AttributeManagementSection';
import * as supabase from '@/lib/supabase';

// Mock the useProductForm hook
jest.mock('../../../context/ProductFormContext', () => ({
  useProductForm: () => ({
    formData: { base_sku: 'TEST' },
    updateFormData: jest.fn(),
  }),
}));

// Mock the useAuth hook
jest.mock('@/contexts/auth-context', () => ({
  useAuth: () => ({
    user: { id: 'test-user-id' },
  }),
}));

// Mock the supabase functions
jest.mock('@/lib/supabase', () => ({
  getProductAttributes: jest.fn(),
  createProductAttribute: jest.fn(),
  getProductAttributeValues: jest.fn(),
  createProductAttributeValue: jest.fn(),
}));

describe('AttributeManagementSection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the component correctly', () => {
    (supabase.getProductAttributes as jest.Mock).mockResolvedValue([]);
    
    render(<AttributeManagementSection />);
    
    expect(screen.getByText('Product Attributes')).toBeInTheDocument();
    expect(screen.getByText('Define the attributes that will create your product variants')).toBeInTheDocument();
  });

  it('should allow adding a new attribute', async () => {
    (supabase.getProductAttributes as jest.Mock).mockResolvedValue([]);
    (supabase.createProductAttribute as jest.Mock).mockResolvedValue({ id: 'attr-1' });
    
    render(<AttributeManagementSection />);
    
    // Type a new attribute name
    const input = screen.getByRole('combobox');
    fireEvent.click(input);
    
    // Since we're using a Combobox, we need to type in the search input
    const searchInput = screen.getByPlaceholderText('Search attributes...');
    fireEvent.change(searchInput, { target: { value: 'Size' } });
    
    // Click the "Add Size" option
    const addOption = screen.getByText('Add "Size"');
    fireEvent.click(addOption);
    
    // Click the Add Attribute button
    const addButton = screen.getByText('Add Attribute');
    fireEvent.click(addButton);
    
    // Wait for the attribute to be added
    await waitFor(() => {
      expect(screen.getByText('Size')).toBeInTheDocument();
    });
  });

  it('should allow adding values to an attribute', async () => {
    (supabase.getProductAttributes as jest.Mock).mockResolvedValue([]);
    (supabase.createProductAttribute as jest.Mock).mockResolvedValue({ id: 'attr-1' });
    (supabase.getProductAttributeValues as jest.Mock).mockResolvedValue([]);
    (supabase.createProductAttributeValue as jest.Mock).mockResolvedValue({});
    
    render(<AttributeManagementSection />);
    
    // Add an attribute first
    const input = screen.getByRole('combobox');
    fireEvent.click(input);
    
    const searchInput = screen.getByPlaceholderText('Search attributes...');
    fireEvent.change(searchInput, { target: { value: 'Size' } });
    
    const addOption = screen.getByText('Add "Size"');
    fireEvent.click(addOption);
    
    const addButton = screen.getByText('Add Attribute');
    fireEvent.click(addButton);
    
    // Wait for the attribute to be added
    await waitFor(() => {
      expect(screen.getByText('Size')).toBeInTheDocument();
    });
    
    // Click the "Add Values" button
    const addValuesButton = screen.getByText('Add Values');
    fireEvent.click(addValuesButton);
    
    // Add a value
    const valueInput = screen.getByPlaceholderText('Search size values...');
    fireEvent.change(valueInput, { target: { value: 'Large' } });
    
    const addValueButton = screen.getByText('Add');
    fireEvent.click(addValueButton);
    
    // Wait for the value to be added
    await waitFor(() => {
      expect(screen.getByText('Large')).toBeInTheDocument();
    });
  });
});