import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AttributeManagementSection } from '../AttributeManagementSection';
import { ProductFormProvider } from '../../context/ProductFormContext';
import { AuthProvider } from '@/contexts/auth-context';
import { getSupabaseClient } from '@/lib/supabase';

// Mock the Supabase client
jest.mock('@/lib/supabase', () => ({
  getSupabaseClient: jest.fn(),
  getProductAttributes: jest.fn(),
  createProductAttribute: jest.fn(),
  getProductAttributeValues: jest.fn(),
  createProductAttributeValue: jest.fn()
}));

// Mock the useAuth hook
jest.mock('@/contexts/auth-context', () => ({
  useAuth: () => ({
    user: { id: 'test-user-id' }
  })
}));

describe('AttributeManagementSection', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('should render the attribute management section', () => {
    render(
      <AuthProvider>
        <ProductFormProvider>
          <AttributeManagementSection />
        </ProductFormProvider>
      </AuthProvider>
    );

    expect(screen.getByText('Product Attributes')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Select or add attribute name...')).toBeInTheDocument();
  });

  it('should allow adding a new attribute', async () => {
    const mockCreateProductAttribute = jest.fn().mockResolvedValue({ id: 'new-attribute-id' });
    const mockGetProductAttributes = jest.fn().mockResolvedValue([]);
    
    // Mock the Supabase functions
    require('@/lib/supabase').createProductAttribute = mockCreateProductAttribute;
    require('@/lib/supabase').getProductAttributes = mockGetProductAttributes;

    render(
      <AuthProvider>
        <ProductFormProvider>
          <AttributeManagementSection />
        </ProductFormProvider>
      </AuthProvider>
    );

    // Type a new attribute name
    const input = screen.getByPlaceholderText('Select or add attribute name...');
    fireEvent.change(input, { target: { value: 'Material' } });

    // Click the "Add Attribute" button
    const addButton = screen.getByText('Add Attribute');
    fireEvent.click(addButton);

    // Wait for the attribute to be added
    await waitFor(() => {
      expect(mockCreateProductAttribute).toHaveBeenCalledWith('test-user-id', 'Material');
    });
  });

  it('should allow adding values to an attribute', async () => {
    const mockCreateProductAttributeValue = jest.fn().mockResolvedValue({ id: 'new-value-id' });
    
    // Mock the Supabase functions
    require('@/lib/supabase').createProductAttributeValue = mockCreateProductAttributeValue;

    render(
      <AuthProvider>
        <ProductFormProvider>
          <AttributeManagementSection />
        </ProductFormProvider>
      </AuthProvider>
    );

    // First add an attribute
    const input = screen.getByPlaceholderText('Select or add attribute name...');
    fireEvent.change(input, { target: { value: 'Color' } });

    const addButton = screen.getByText('Add Attribute');
    fireEvent.click(addButton);

    // Wait for the UI to update
    await waitFor(() => {
      expect(screen.getByText('Color')).toBeInTheDocument();
    });

    // Click "Add Values" button
    const addValuesButton = screen.getByText('Add Values');
    fireEvent.click(addValuesButton);

    // Type a value and add it
    const valueInput = screen.getByPlaceholderText('Select or add color value...');
    fireEvent.change(valueInput, { target: { value: 'Red' } });

    const addValueButton = screen.getByText('Add');
    fireEvent.click(addValueButton);

    // Wait for the value to be added
    await waitFor(() => {
      expect(mockCreateProductAttributeValue).toHaveBeenCalledWith('test-user-id', expect.any(String), 'Red');
    });
  });
});