'use client'

import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { RotateCcw } from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { type ProductFilters } from '../product-filters'

interface InventoryAdvancedFiltersProps {
  filters: ProductFilters
  setFilters: (filters: ProductFilters) => void
  activeFilterCount: number
  clearAllFilters: () => void
  availableOptions: {
    categories: { value: string; label: string }[]
    suppliers: { value: string; label: string }[]
    stockStatus: { value: string; label: string }[]
  }
  setIsExpanded: (isExpanded: boolean) => void
}

export function InventoryAdvancedFilters({
  filters,
  setFilters,
  activeFilterCount,
  clearAllFilters,
  availableOptions,
  setIsExpanded
}: InventoryAdvancedFiltersProps) {
  return (
    <div className="border border-gray-200 rounded-lg p-4 bg-white mb-4">
      <div className="text-xs font-medium mb-3 pb-2 border-b border-gray-100">Advanced Filters</div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
        {/* Stock Status filter - CHANGED TO SELECT */}
        <div className="space-y-1">
          <Label className="text-xs font-medium">Stock Status</Label>
          <Select 
            value={filters.stockStatus[0] || ""} 
            onValueChange={(value) => setFilters({
              ...filters,
              stockStatus: value ? [value as 'in_stock' | 'low_stock' | 'out_of_stock'] : []
            })}
          >
            <SelectTrigger className="h-8 text-xs">
              <SelectValue placeholder="Select status" className="text-xs" />
            </SelectTrigger>
            <SelectContent>
              {availableOptions.stockStatus.map(status => (
                <SelectItem key={status.value} value={status.value} className="text-xs">
                  {status.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {/* Category filter - CHANGED TO SELECT */}
        <div className="space-y-1">
          <Label className="text-xs font-medium">Categories</Label>
          <Select 
            value={filters.categories[0] || ""} 
            onValueChange={(value) => setFilters({
              ...filters,
              categories: value ? [value] : []
            })}
          >
            <SelectTrigger className="h-8 text-xs">
              <SelectValue placeholder="Select categories" className="text-xs" />
            </SelectTrigger>
            <SelectContent>
              {availableOptions.categories.map(category => (
                <SelectItem key={category.value} value={category.value} className="text-xs">
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {/* Supplier filter - CHANGED TO SELECT */}
        <div className="space-y-1">
          <Label className="text-xs font-medium">Suppliers</Label>
          <Select 
            value={filters.suppliers[0] || ""} 
            onValueChange={(value) => setFilters({
              ...filters,
              suppliers: value ? [value] : []
            })}
          >
            <SelectTrigger className="h-8 text-xs">
              <SelectValue placeholder="Select suppliers" className="text-xs" />
            </SelectTrigger>
            <SelectContent>
              {availableOptions.suppliers.map(supplier => (
                <SelectItem key={supplier.value} value={supplier.value} className="text-xs">
                  {supplier.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {/* Quantity Range filter */}
        <div className="space-y-1">
          <Label className="text-xs font-medium">Quantity Range</Label>
          <div className="flex gap-1">
            <Input
              type="number"
              placeholder="Min"
              value={filters.quantityRange.min ?? ''}
              onChange={(e) => setFilters({
                ...filters,
                quantityRange: {
                  ...filters.quantityRange,
                  min: e.target.value === '' ? null : Number(e.target.value)
                }
              })}
              className="h-8 text-xs placeholder:text-xs"
            />
            <Input
              type="number"
              placeholder="Max"
              value={filters.quantityRange.max ?? ''}
              onChange={(e) => setFilters({
                ...filters,
                quantityRange: {
                  ...filters.quantityRange,
                  max: e.target.value === '' ? null : Number(e.target.value)
                }
              })}
              className="h-8 text-xs placeholder:text-xs"
            />
          </div>
        </div>
      </div>
      
      {/* Action buttons */}
      <div className="mt-4 flex justify-end gap-2">
        <Button 
          variant="outline" 
          size="sm" 
          className="h-7 text-xs"
          onClick={clearAllFilters}
          disabled={activeFilterCount === 0}
        >
          <RotateCcw className="h-3 w-3 mr-1.5" />
          Reset
        </Button>
        <Button 
          size="sm" 
          className="h-7 text-xs"
          onClick={() => setIsExpanded(false)}
        >
          Apply Filters
        </Button>
      </div>
    </div>
  )
}