'use client'

import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { 
  MoreHorizontal,
  ChevronDown,
  ChevronRight as ChevronRightIcon
} from 'lucide-react'
import { type ExtendedProductRow, type InventoryItem } from '@/components/products/inventory/types'

interface InventoryTableRowProps {
  item: InventoryItem
  product: ExtendedProductRow | undefined
  isVariableProduct: boolean
  productVariants: any[]
  isExpanded: boolean
  expandedProducts: Record<string, boolean>
  setExpandedProducts: (expanded: Record<string, boolean>) => void
  openMenuId: string | null
  setOpenMenuId: (id: string | null) => void
  checkItemStockHistory: (item: InventoryItem) => Promise<boolean>
  itemsWithHistory: Record<string, boolean>
  setSelectedItem: (item: InventoryItem | null) => void
  setIsAdjustStockModalOpen: (isOpen: boolean) => void
  setIsStockHistoryModalOpen: (isOpen: boolean) => void
  setIsPurchaseOrderModalOpen: (isOpen: boolean) => void
}

export function InventoryTableRow({
  item,
  product,
  isVariableProduct,
  productVariants,
  isExpanded,
  expandedProducts,
  setExpandedProducts,
  openMenuId,
  setOpenMenuId,
  checkItemStockHistory,
  itemsWithHistory,
  setSelectedItem,
  setIsAdjustStockModalOpen,
  setIsStockHistoryModalOpen,
  setIsPurchaseOrderModalOpen
}: InventoryTableRowProps) {
  return (
    <>
      <tr className="hover:bg-gray-50">
        <td className="py-4 px-6">
          <div className="max-w-[220px]">
            <div className="font-medium text-gray-900 truncate text-xs flex items-center gap-1" title={item.name}>
              {isVariableProduct && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-4 w-4 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                  onClick={(e) => {
                    e.stopPropagation()
                    setExpandedProducts({
                      ...expandedProducts,
                      [item.productId]: !isExpanded
                    })
                  }}
                >
                  {isExpanded ? <ChevronDown className="h-3.5 w-3.5" /> : <ChevronRightIcon className="h-3.5 w-3.5" />}
                </Button>
              )}
              {item.name}
              {isVariableProduct && (
                <span className="ml-2 inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-blue-600 bg-blue-100 rounded-full">
                  {productVariants.length}
                </span>
              )}
            </div>
            <div className="text-xs text-gray-500 space-y-1 mt-1">
              <div className="text-2xs text-gray-500">
                {item.sku}
              </div>
            </div>
          </div>
        </td>
        <td className="py-4 px-6 text-xs text-gray-700">
          {item.stockOnHand}
        </td>
        <td className="py-4 px-6 text-xs text-gray-700">
          {item.committed}
        </td>
        <td className="py-4 px-6 text-xs text-gray-700">
          {item.available}
        </td>
        <td className="py-4 px-6 text-xs text-gray-700">
          {item.incoming}
        </td>
        <td className="py-4 px-6 text-left">
          <div className="flex flex-col items-start space-y-1">
            <span className={cn(
              "inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium",
              item.status === 'in_stock' && "bg-green-50 text-green-700 border border-green-100",
              item.status === 'low_stock' && "bg-yellow-50 text-yellow-700 border border-yellow-100",
              item.status === 'out_of_stock' && "bg-red-50 text-red-700 border border-red-100"
            )}>
              {item.status === 'in_stock' && 'In Stock'}
              {item.status === 'low_stock' && 'Low Stock'}
              {item.status === 'out_of_stock' && 'Out of Stock'}
            </span>
          </div>
        </td>
        <td className="py-4 px-6 text-center">
          <div className="flex items-center justify-center relative w-full">
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-8 w-8 p-0"
              onClick={async (e) => {
                e.stopPropagation()
                // Generate a unique ID for this product menu
                const menuId = `product-${item.id}`
                
                // Check stock history for this product
                await checkItemStockHistory(item)
                
                setOpenMenuId(openMenuId === menuId ? null : menuId)
              }}
            >
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
            
            {openMenuId === `product-${item.id}` && (
              <div className="absolute right-0 top-full z-10 mt-1 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dropdown-menu">
                <div className="py-1">
                  <button
                    className="block px-4 py-2 text-xs text-gray-700 hover:bg-gray-100 w-full text-left"
                    onClick={() => {
                      // Handle Adjust Stock
                      // Set both states and then open modal
                      setSelectedItem(item)
                      setOpenMenuId(null)
                      // Use a small delay to ensure state updates are processed
                      setTimeout(() => {
                        setIsAdjustStockModalOpen(true)
                      }, 10)
                    }}
                  >
                    Adjust Stock
                  </button>
                  <button
                    className={`block px-4 py-2 text-xs w-full text-left ${itemsWithHistory[item.id] ? 'text-gray-700 hover:bg-gray-100' : 'text-gray-400 cursor-not-allowed'}`}
                    onClick={() => {
                      // Handle View History
                      if (!itemsWithHistory[item.id]) return
                      setSelectedItem(item)
                      setOpenMenuId(null)
                      // Use a small delay to ensure state updates are processed
                      setTimeout(() => {
                        setIsStockHistoryModalOpen(true)
                      }, 10)
                    }}
                    disabled={!itemsWithHistory[item.id]}
                  >
                    View History
                  </button>
                  <button
                    className="block px-4 py-2 text-xs text-gray-700 hover:bg-gray-100 w-full text-left"
                    onClick={() => {
                      // Handle Create Purchase Order
                      setSelectedItem(item)
                      setOpenMenuId(null)
                      // Use a small delay to ensure state updates are processed
                      setTimeout(() => {
                        setIsPurchaseOrderModalOpen(true)
                      }, 10)
                    }}
                  >
                    Create Purchase Order
                  </button>
                </div>
              </div>
            )}
          </div>
        </td>
      </tr>
      
      {/* Expanded row for variable product variants - only show when expanded */}
      {isVariableProduct && isExpanded && productVariants.length > 0 && (
        productVariants.map((variant) => {
          // Calculate variant stock status
          const variantStockQty = variant.stock_quantity || 0
          const variantLowStockThreshold = variant.low_stock_threshold || 10
          let variantStatus: 'in_stock' | 'low_stock' | 'out_of_stock' = 'in_stock'
          
          if (variantStockQty === 0) {
            variantStatus = 'out_of_stock'
          } else if (variantStockQty <= variantLowStockThreshold) {
            variantStatus = 'low_stock'
          }
          
          // Check if this variant should be shown based on current filters
          // Note: This is a simplified version - in the full implementation, 
          // you might want to pass the filters as props
          const showVariant = true
          
          // Only render the variant row if it matches the filter criteria or if no filter is applied
          if (!showVariant) return null
          
          return (
            <tr key={variant.id} className="border-t hover:bg-gray-50/50 transition-colors bg-gray-50">
              <td className="py-3 px-6 pl-14">
                <div className="font-medium text-xs">
                  {variant.variant_name || 'Unnamed Variant'}
                </div>
                <div className="text-2xs text-gray-500 mt-1">
                  {variant.size && <span className="mr-2">Size: {variant.size}</span>}
                  {variant.color && <span className="mr-2">Color: {variant.color}</span>}
                  {variant.material && <span className="mr-2">Material: {variant.material}</span>}
                  {variant.style && <span className="mr-2">Style: {variant.style}</span>}
                </div>
                <div className="text-2xs text-gray-500">
                  {variant.sku || 'No SKU'}
                </div>
              </td>
              
              <td className="py-3 px-6 text-xs text-gray-700">
                {variant.stock_quantity || 0}
              </td>
              
              <td className="py-3 px-6 text-xs text-gray-700">
                {variant.reserved_quantity || 0}
              </td>
              
              <td className="py-3 px-6 text-xs text-gray-700">
                {(variant.stock_quantity || 0) - (variant.reserved_quantity || 0)}
              </td>
              
              <td className="py-3 px-6 text-xs text-gray-700">
                0
              </td>
              
              <td className="py-3 px-6 text-left">
                <span className={cn(
                  "inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium",
                  variantStatus === 'in_stock' && "bg-green-50 text-green-700 border border-green-100",
                  variantStatus === 'low_stock' && "bg-yellow-50 text-yellow-700 border border-yellow-100",
                  variantStatus === 'out_of_stock' && "bg-red-50 text-red-700 border border-red-100"
                )}>
                  {variantStatus === 'in_stock' && 'In Stock'}
                  {variantStatus === 'low_stock' && 'Low Stock'}
                  {variantStatus === 'out_of_stock' && 'Out of Stock'}
                </span>
              </td>
              
              <td className="py-3 px-6 text-center">
                <div className="flex items-center justify-center relative w-full">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-7 w-7 p-0"
                    onClick={async (e) => {
                      e.stopPropagation()
                      // Generate a unique ID for this variant menu
                      const menuId = `variant-${variant.id}`
                  
                      // Check stock history for this variant
                      const item = {
                        id: variant.id,
                        productId: variant.product_id,
                        variantId: variant.id,
                        name: `${product?.name || ''} - ${variant.variant_name || ''}`,
                        sku: variant.sku || '',
                        stockOnHand: variant.stock_quantity || 0,
                        committed: variant.reserved_quantity || 0,
                        available: (variant.stock_quantity || 0) - (variant.reserved_quantity || 0),
                        incoming: 0,
                        status: variantStatus,
                        type: 'variant' as const,
                        lowStockThreshold: variant.low_stock_threshold || 10
                      }
                  
                      await checkItemStockHistory(item)
                  
                      setOpenMenuId(openMenuId === menuId ? null : menuId)
                    }}
                  >
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-3.5 w-3.5" />
                  </Button>
                  
                  {openMenuId === `variant-${variant.id}` && (
                    <div className="absolute right-0 top-full z-10 mt-1 w-48 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dropdown-menu">
                      <div className="py-1">
                        <button
                          className="block px-4 py-2 text-xs text-gray-700 hover:bg-gray-100 w-full text-left"
                          onClick={() => {
                            // Handle Adjust Stock
                            const item = {
                              id: variant.id,
                              productId: variant.product_id,
                              variantId: variant.id,
                              name: `${product?.name || ''} - ${variant.variant_name || ''}`,
                              sku: variant.sku || '',
                              stockOnHand: variant.stock_quantity || 0,
                              committed: variant.reserved_quantity || 0,
                              available: (variant.stock_quantity || 0) - (variant.reserved_quantity || 0),
                              incoming: 0,
                              status: variantStatus,
                              type: 'variant' as const,
                              lowStockThreshold: variant.low_stock_threshold || 10
                            }
                            console.log('Variant Adjust Stock clicked, item:', item)
                            setSelectedItem(item)
                            setOpenMenuId(null)
                            // Use a small delay to ensure state updates are processed
                            setTimeout(() => {
                              setIsAdjustStockModalOpen(true)
                            }, 10)
                          }}
                        >
                          Adjust Stock
                        </button>
                        <button
                          className={`block px-4 py-2 text-xs w-full text-left ${itemsWithHistory[variant.id] ? 'text-gray-700 hover:bg-gray-100' : 'text-gray-400 cursor-not-allowed'}`}
                          onClick={() => {
                            // Handle View History
                            if (!itemsWithHistory[variant.id]) return
                            console.log('Variant View History clicked, item:', item)
                            setSelectedItem(item)
                            setOpenMenuId(null)
                            // Use a small delay to ensure state updates are processed
                            setTimeout(() => {
                              setIsStockHistoryModalOpen(true)
                            }, 10)
                          }}
                          disabled={!itemsWithHistory[variant.id]}
                        >
                          View History
                        </button>
                        <button
                          className="block px-4 py-2 text-xs text-gray-700 hover:bg-gray-100 w-full text-left"
                          onClick={() => {
                            // Handle Create Purchase Order
                            console.log('Variant Create Purchase Order clicked, item:', item)
                            setSelectedItem(item)
                            setOpenMenuId(null)
                            // Use a small delay to ensure state updates are processed
                            setTimeout(() => {
                              setIsPurchaseOrderModalOpen(true)
                            }, 10)
                          }}
                        >
                          Create Purchase Order
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </td>
            </tr>
          )
        })
      )}
    </>
  )
}