'use client'

import { createSupabaseClient } from '@/lib/supabase'
import { type InventoryItem } from './types'

export async function checkItemStockHistory(
  item: InventoryItem,
  itemsWithHistory: Record<string, boolean>,
  setItemsWithHistory: (items: Record<string, boolean>) => void
): Promise<boolean> {
  // Check if we've already checked this item
  if (itemsWithHistory.hasOwnProperty(item.id)) {
    return itemsWithHistory[item.id];
  }

  try {
    const supabase = createSupabaseClient();
    
    // Get user ID safely
    let userId: string | undefined;
    try {
      const { data: { user } } = await supabase.auth.getUser();
      userId = user?.id;
    } catch (userError) {
      console.error('Error getting user for stock history check:', userError);
      return false;
    }

    if (!userId) {
      return false;
    }

    // Query stock history records
    let query = supabase
      .from('stock_history')
      .select('id', { count: 'exact', head: true })
      .eq('user_id', userId)
      .limit(1);

    // Filter by product or variant
    if (item.variantId) {
      query = query.eq('variant_id', item.variantId);
    } else {
      query = query.eq('product_id', item.productId);
    }

    const { count, error } = await query;

    if (error) throw error;

    const hasHistory = (count || 0) > 0;
    
    // Update state
    setItemsWithHistory({
      ...itemsWithHistory,
      [item.id]: hasHistory
    });

    return hasHistory;
  } catch (error) {
    console.error('Error checking stock history:', error);
    return false;
  }
}