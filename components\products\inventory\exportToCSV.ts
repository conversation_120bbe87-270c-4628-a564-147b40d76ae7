'use client'

import { type ExtendedProductRow, type InventoryItem } from './types'
import { type ProductFilters } from '../product-filters'
import { createSupabaseClient } from '@/lib/supabase'
import { toast } from '@/components/ui/use-toast'

export async function exportToCSV(
  products: ExtendedProductRow[],
  inventoryItems: InventoryItem[],
  searchQuery: string,
  filters: ProductFilters
) {
  try {
    // Access filtered inventory items directly (since this function is called after they're defined)
    const currentFilteredItems = [...inventoryItems]
      .filter(item => item.type === 'product')
      .sort((a, b) => {
        const productA = products.find(p => p.id === a.productId);
        const productB = products.find(p => p.id === b.productId);
        
        // Sort by created_at (newest first)
        if (productA && productB) {
          const dateA = productA.created_at ? new Date(productA.created_at).getTime() : 0;
          const dateB = productB.created_at ? new Date(productB.created_at).getTime() : 0;
          return dateB - dateA; // Descending order (newest first)
        }
        return 0;
      });
    
    // Apply text search filter
    let filteredItems = currentFilteredItems;
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filteredItems = filteredItems.filter(item => 
        item.name.toLowerCase().includes(query) ||
        item.sku.toLowerCase().includes(query)
      )
    }
    
    // Apply status filter - but for variable products, check if ANY variant matches the filter
    if (filters.stockStatus.length > 0) {
      filteredItems = filteredItems.filter(item => {
        // For simple products, check the product status directly
        if (!products.find(p => p.id === item.productId)?.has_variants) {
          return filters.stockStatus.includes(item.status)
        }
        
        // For variable products, check if any variant matches the filter
        const product = products.find(p => p.id === item.productId);
        if (product?.variants && product.variants.length > 0) {
          // Check if any variant has a status that matches the filter
          return product.variants.some(variant => {
            const variantStockQty = variant.stock_quantity || 0;
            const variantLowStockThreshold = variant.low_stock_threshold || 10;
            let variantStatus: 'in_stock' | 'low_stock' | 'out_of_stock' = 'in_stock';
            
            if (variantStockQty === 0) {
              variantStatus = 'out_of_stock';
            } else if (variantStockQty <= variantLowStockThreshold) {
              variantStatus = 'low_stock';
            }
            
            return filters.stockStatus.includes(variantStatus);
          });
        }
        
        // If no variants, fall back to product status
        return filters.stockStatus.includes(item.status)
      })
    }
    
    // Create CSV header with metadata
    const metadataLines = [
      '# Inventory Report',
      `# Generated on: ${new Date().toISOString()}`,
      `# Total Items: ${filteredItems.length}`,
      '#',
      '# Report Data:'
    ]
    
    // CSV header row
    const headers = [
      'Product Name', 'SKU', 'Stock on Hand', 'Committed', 'Available', 
      'Incoming', 'Status', 'Type', 'Low Stock Threshold'
    ]
    
    // CSV data rows with proper escaping
    const dataRows = filteredItems.map(item => {
      // Get the full product object
      const product = products.find(p => p.id === item.productId)
      
      return [
        `"${(item.name || '').replace(/"/g, '""')}"`,
        item.sku || '',
        item.stockOnHand,
        item.committed,
        item.available,
        item.incoming,
        item.status === 'in_stock' ? 'In Stock' : 
        item.status === 'low_stock' ? 'Low Stock' : 'Out of Stock',
        item.type,
        item.lowStockThreshold
      ].join(',')
    })
    
    // Combine all parts
    const csvContent = [
      ...metadataLines,
      headers.join(','),
      ...dataRows
    ].join('\n')

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `inventory-export-${new Date().toISOString().split('T')[0]}.csv`
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    // Show success message using toast if available
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('toast', {
        detail: {
          title: "CSV Export Complete",
          description: `Successfully exported ${filteredItems.length} inventory items to CSV format.`
        }
      }));
    }
  } catch (error) {
    console.error('CSV export error:', error)
    // Show error message using toast if available
    if (typeof window !== 'undefined' && window.dispatchEvent) {
      window.dispatchEvent(new CustomEvent('toast', {
        detail: {
          title: "CSV Export Failed",
          description: "Failed to export inventory. Please try again.",
          variant: "destructive"
        }
      }));
    }
  }
}