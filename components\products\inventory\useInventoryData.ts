'use client'

import { useMemo } from 'react'
import { type ExtendedProductRow, type InventoryItem } from './types'
import { type ProductFilters } from '../product-filters'

export function useInventoryData(products: ExtendedProductRow[], searchQuery: string, filters: ProductFilters) {
  // Transform products and variants into flat inventory items
  const inventoryItems = useMemo(() => {
    const items: InventoryItem[] = []
    
    // Process all products (both simple and variable)
    products.forEach(product => {
      // Process simple products (without variants)
      if (!product.has_variants) {
        items.push({
          id: product.id,
          productId: product.id,
          variantId: null,
          name: product.name,
          sku: product.base_sku || '',
          stockOnHand: product.stock_quantity || 0,
          committed: 0, // This would come from order data
          available: product.stock_quantity || 0,
          incoming: 0, // This would come from purchase order data
          status: product.stock_quantity === 0 
            ? 'out_of_stock' 
            : (product.stock_quantity || 0) <= (product.low_stock_threshold || 10)
              ? 'low_stock'
              : 'in_stock',
          type: 'product',
          lowStockThreshold: product.low_stock_threshold || 10
        })
      }
      // Process variable products and their variants
      else if (product.has_variants && product.variants && product.variants.length > 0) {
        // Calculate parent product status based on variants
        let productStatus: 'in_stock' | 'low_stock' | 'out_of_stock' = 'in_stock';
        let totalStock = 0;
        let totalCommitted = 0;
        let minThreshold = Infinity;
        
        // Check if all variants are out of stock
        const allOutOfStock = product.variants.every(v => (v.stock_quantity || 0) === 0);
        
        // Check if any variant is out of stock
        const hasOutOfStock = product.variants.some(v => (v.stock_quantity || 0) === 0);
        
        // Check if any variant is low stock
        const hasLowStock = product.variants.some(v => {
          const stockQty = v.stock_quantity || 0;
          const threshold = v.low_stock_threshold || 10;
          return stockQty > 0 && stockQty <= threshold;
        });
        
        // Determine parent product status based on variants
        if (allOutOfStock) {
          productStatus = 'out_of_stock';
        } else if (hasOutOfStock || hasLowStock) {
          productStatus = 'low_stock';
        }
        
        // Calculate totals for parent product
        product.variants.forEach(variant => {
          totalStock += variant.stock_quantity || 0;
          totalCommitted += variant.reserved_quantity || 0;
          minThreshold = Math.min(minThreshold, variant.low_stock_threshold || 10);
        });
        
        // If minThreshold wasn't updated, set to default
        if (minThreshold === Infinity) {
          minThreshold = 10;
        }
        
        items.push({
          id: product.id,
          productId: product.id,
          variantId: null,
          name: product.name,
          sku: product.base_sku || '',
          stockOnHand: totalStock,
          committed: totalCommitted,
          available: totalStock - totalCommitted,
          incoming: 0, // This would come from purchase order data
          status: productStatus,
          type: 'product',
          lowStockThreshold: minThreshold
        })
        
        // Process variants
        product.variants.forEach(variant => {
          items.push({
            id: variant.id,
            productId: variant.product_id,
            variantId: variant.id,
            name: `${product.name} - ${variant.variant_name || ''}`,
            sku: variant.sku,
            stockOnHand: variant.stock_quantity || 0,
            committed: variant.reserved_quantity || 0,
            available: (variant.stock_quantity || 0) - (variant.reserved_quantity || 0),
            incoming: 0, // This would come from purchase order data
            status: variant.stock_quantity === 0 
              ? 'out_of_stock' 
              : (variant.stock_quantity || 0) <= (variant.low_stock_threshold || 10)
                ? 'low_stock'
                : 'in_stock',
            type: 'variant',
            lowStockThreshold: variant.low_stock_threshold || 10
          })
        })
      }
    })
    
    return items
  }, [products])

  // Filter inventory items based on search and filters
  // Only include parent products, not individual variants
  const filteredInventoryItems = useMemo(() => {
    // Start with only product items (not variants) and sort by created_at DESC (newest first)
    let result = [...inventoryItems]
      .filter(item => item.type === 'product')
      .sort((a, b) => {
        const productA = products.find(p => p.id === a.productId);
        const productB = products.find(p => p.id === b.productId);
        
        // Sort by created_at (newest first)
        if (productA && productB) {
          const dateA = productA.created_at ? new Date(productA.created_at).getTime() : 0;
          const dateB = productB.created_at ? new Date(productB.created_at).getTime() : 0;
          return dateB - dateA; // Descending order (newest first)
        }
        return 0;
      });
    
    // Text search
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      result = result.filter(item => 
        item.name.toLowerCase().includes(query) ||
        item.sku.toLowerCase().includes(query)
      )
    }
    
    // Category filter
    if (filters.categories.length > 0) {
      result = result.filter(item => {
        const product = products.find(p => p.id === item.productId);
        return product && product.categories?.name && filters.categories.includes(product.categories.name);
      });
    }
    
    // Supplier filter
    if (filters.suppliers.length > 0) {
      result = result.filter(item => {
        const product = products.find(p => p.id === item.productId);
        return product && product.supplier && filters.suppliers.includes(product.supplier);
      });
    }
    
    // Status filter - but for variable products, check if ANY variant matches the filter
    if (filters.stockStatus.length > 0) {
      result = result.filter(item => {
        // For simple products, check the product status directly
        if (!products.find(p => p.id === item.productId)?.has_variants) {
          return filters.stockStatus.includes(item.status)
        }
        
        // For variable products, check if any variant matches the filter
        const product = products.find(p => p.id === item.productId);
        if (product?.variants && product.variants.length > 0) {
          // Check if any variant has a status that matches the filter
          return product.variants.some(variant => {
            const variantStockQty = variant.stock_quantity || 0;
            const variantLowStockThreshold = variant.low_stock_threshold || 10;
            let variantStatus: 'in_stock' | 'low_stock' | 'out_of_stock' = 'in_stock';
            
            if (variantStockQty === 0) {
              variantStatus = 'out_of_stock';
            } else if (variantStockQty <= variantLowStockThreshold) {
              variantStatus = 'low_stock';
            }
            
            return filters.stockStatus.includes(variantStatus);
          });
        }
        
        // If no variants, fall back to product status
        return filters.stockStatus.includes(item.status)
      })
    }
    
    // Quantity range filter
    if (filters.quantityRange.min !== null || filters.quantityRange.max !== null) {
      result = result.filter(item => {
        const product = products.find(p => p.id === item.productId);
        if (!product) return false;
        
        // For simple products, use the product's stock quantity
        if (!product.has_variants) {
          const stockQty = product.stock_quantity || 0;
          const min = filters.quantityRange.min !== null ? filters.quantityRange.min : -Infinity;
          const max = filters.quantityRange.max !== null ? filters.quantityRange.max : Infinity;
          return stockQty >= min && stockQty <= max;
        }
        
        // For variable products, use the total stock from all variants
        if (product.variants && product.variants.length > 0) {
          const totalStock = product.variants.reduce((total, variant) => total + (variant.stock_quantity || 0), 0);
          const min = filters.quantityRange.min !== null ? filters.quantityRange.min : -Infinity;
          const max = filters.quantityRange.max !== null ? filters.quantityRange.max : Infinity;
          return totalStock >= min && totalStock <= max;
        }
        
        return false;
      });
    }
    
    return result
  }, [inventoryItems, searchQuery, filters, products])

  // Extract unique options from products and variants
  const availableOptions = useMemo(() => {
    const suppliers = new Set<string>()
    const categories = new Set<string>()
    
    products.forEach(product => {
      if (product.supplier) suppliers.add(product.supplier)
      // Fix: Access category name correctly from the categories relationship
      if (product.categories?.name) categories.add(product.categories.name)
    })
    
    return {
      categories: Array.from(categories).map(category => ({ value: category, label: category })),
      suppliers: Array.from(suppliers).map(supplier => ({ value: supplier, label: supplier })),
      stockStatus: [
        { value: 'in_stock', label: 'In Stock' },
        { value: 'low_stock', label: 'Low Stock' },
        { value: 'out_of_stock', label: 'Out of Stock' }
      ]
    }
  }, [products])

  return {
    inventoryItems,
    filteredInventoryItems,
    availableOptions
  }
}