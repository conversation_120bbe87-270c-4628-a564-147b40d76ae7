'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  CalendarDays, 
  TrendingUp, 
  TrendingDown, 
  BarChart3,
  Download,
  Calendar,
  Filter,
  FileSpreadsheet,
  FileText,
  ChevronDown,
  Package,
  DollarSign,
  AlertTriangle,
  X,
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar
} from 'recharts'
import { createSupabaseClient, type ProductRow, type ProductVariantRow, getAllProductsForUser } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { type ProductFilters } from '@/components/products/products-catalog/product-filters'

// Period selection options
const PERIOD_OPTIONS = [
  { value: 'month' as const, label: 'This Month', icon: CalendarDays },
  { value: 'quarter' as const, label: 'This Quarter', icon: BarChart3 },
  { value: 'year' as const, label: 'This Year', icon: Calendar },
]

// Chart colors for consistency
const CHART_COLORS = [
  '#3B82F6', // blue
  '#10B981', // green
  '#F59E0B', // yellow
  '#EF4444', // red
  '#8B5CF6', // purple
  '#06B6D4', // cyan
  '#F97316', // orange
  '#EC4899'  // pink
]

interface ProductWithVariants extends ProductRow {
  variants?: ProductVariantRow[]
  category_name?: string | null
}

interface AnalyticsTabProps {
  isMobile?: boolean
  className?: string
  contextualFilters?: any // ProductFilters type
  filteredProductIds?: string[]
}

export function AnalyticsTab({ 
  isMobile = false, 
  className,
  contextualFilters,
  filteredProductIds = []
}: AnalyticsTabProps) {
  const [products, setProducts] = useState<ProductWithVariants[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState<'month' | 'quarter' | 'year'>('month')
  const [isExporting, setIsExporting] = useState(false)
  
  const { user } = useAuth()
  const { formatCurrency } = useCurrency()
  const { toast } = useToast()
  const supabase = createSupabaseClient()

  // Load products on component mount
  useEffect(() => {
    if (user) {
      loadProducts()
    }
  }, [user])

  const loadProducts = async () => {
    if (!user?.id) return
    
    setIsLoading(true)
    try {
      const data = await getAllProductsForUser(user.id)

      // Transform data to include category name and variants
      const transformedProducts = (data || []).map(product => ({
        ...product,
        category_name: product.categories?.name || null,
        variants: product.product_variants || []
      }))
      
      // Apply contextual filtering if filteredProductIds are provided
      const contextualProducts = filteredProductIds.length > 0 
        ? transformedProducts.filter(product => filteredProductIds.includes(product.id))
        : transformedProducts
      
      setProducts(contextualProducts)
    } catch (error) {
      console.error('Error loading products:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to load products. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Process analytics data
  const analyticsData = useMemo(() => {
    // For now, we'll create simplified analytics data
    // In a real implementation, this would be more complex with time-based data
    
    // Category breakdown
    const categoryMap = new Map<string, { count: number; value: number; color: string }>()
    let totalInventoryValue = 0
    let totalProducts = products.length
    let lowStockProducts = 0
    let outOfStockProducts = 0
    
    // Margin analysis data
    let totalMargin = 0
    let totalCost = 0
    let totalRevenue = 0
    const topMarginProducts = [] as { name: string; margin: number; value: number }[]
    const lowMarginProducts = [] as { name: string; margin: number; value: number }[]
    
    products.forEach((product, index) => {
      const categoryName = (product as any).category_name || 'Uncategorized';
      let stockQty, unitValue, totalValue, cost, revenue, profit, margin;
      
      // Handle variable products differently from simple products
      if (product.has_variants && product.variants && product.variants.length > 0) {
        // For variable products, calculate totals from all variants
        stockQty = product.variants.reduce((sum, variant) => sum + (variant.stock_quantity || 0), 0);
        
        // Calculate total inventory value from variants
        totalValue = product.variants.reduce((sum, variant) => {
          const variantPrice = variant.effective_price || variant.price || 0;
          const variantQuantity = variant.stock_quantity || 0;
          return sum + (variantPrice * variantQuantity);
        }, 0);
        
        // Calculate weighted average unit value for display purposes
        unitValue = stockQty > 0 ? totalValue / stockQty : 0;
        
        // Calculate total cost from variants
        cost = product.variants.reduce((sum, variant) => {
          const variantCost = (product.base_cost || 0) + (product.packaging_cost || 0);
          const variantQuantity = variant.stock_quantity || 0;
          return sum + (variantCost * variantQuantity);
        }, 0);
        
        // Calculate revenue and profit from variants
        revenue = totalValue;
        profit = revenue - cost;
        margin = cost > 0 ? (profit / cost) * 100 : 0;
        
        // Check stock status for variants
        const hasOutOfStock = product.variants.some(variant => (variant.stock_quantity || 0) === 0);
        const hasLowStock = product.variants.some(variant => {
          const qty = variant.stock_quantity || 0;
          const threshold = variant.low_stock_threshold || product.low_stock_threshold || 10;
          return qty > 0 && qty <= threshold;
        });
        
        if (hasOutOfStock) {
          outOfStockProducts++;
        } else if (hasLowStock) {
          lowStockProducts++;
        }
      } else {
        // For simple products, use existing logic
        stockQty = product.stock_quantity || 0;
        const lowStockThreshold = product.low_stock_threshold || 10;
        unitValue = product.effective_price || product.price || 0;
        totalValue = unitValue * stockQty;
        
        if (stockQty === 0) outOfStockProducts++;
        else if (stockQty <= lowStockThreshold) lowStockProducts++;
        
        // Margin calculations for simple products
        cost = ((product.base_cost || 0) + (product.packaging_cost || 0)) * stockQty;
        revenue = unitValue * stockQty;
        profit = revenue - cost;
        margin = cost > 0 ? (profit / cost) * 100 : 0;
      }
      
      totalInventoryValue += totalValue;
      totalMargin += profit;
      totalCost += cost;
      totalRevenue += revenue;
      
      // Top margin products
      if (margin > 0) {
        topMarginProducts.push({
          name: product.name,
          margin,
          value: totalValue
        })
      }
      
      // Low margin products
      if (margin < 15) {
        lowMarginProducts.push({
          name: product.name,
          margin,
          value: totalValue
        })
      }
      
      if (categoryMap.has(categoryName)) {
        const current = categoryMap.get(categoryName)!
        categoryMap.set(categoryName, {
          count: current.count + 1,
          value: current.value + totalValue,
          color: current.color
        })
      } else {
        categoryMap.set(categoryName, {
          count: 1,
          value: totalValue,
          color: CHART_COLORS[index % CHART_COLORS.length]
        })
      }
    })
    
    // Calculate average margin
    const averageMargin = totalCost > 0 ? (totalMargin / totalCost) * 100 : 0
    
    // Sort top and low margin products
    topMarginProducts.sort((a, b) => b.margin - a.margin)
    lowMarginProducts.sort((a, b) => a.margin - b.margin)
    
    // Margin distribution (simplified ranges)
    const marginDistribution = [
      { range: '0-10%', count: lowMarginProducts.filter(p => p.margin >= 0 && p.margin < 10).length, color: '#EF4444' },
      { range: '10-15%', count: lowMarginProducts.filter(p => p.margin >= 10 && p.margin < 15).length, color: '#F59E0B' },
      { range: '15-25%', count: topMarginProducts.filter(p => p.margin >= 15 && p.margin < 25).length, color: '#3B82F6' },
      { range: '25%+', count: topMarginProducts.filter(p => p.margin >= 25).length, color: '#10B981' }
    ]
    
    // Convert to array and sort by value
    const categoryBreakdown = Array.from(categoryMap.entries())
      .map(([category, data]) => ({
        category,
        count: data.count,
        value: data.value,
        percentage: totalInventoryValue > 0 ? (data.value / totalInventoryValue) * 100 : 0,
        color: data.color
      }))
      .sort((a, b) => b.value - a.value)
    
    // Top selling products (by stock value)
    const topProducts = [...products]
      .map(product => {
        let value, stock, productMargin;
        
        if (product.has_variants && product.variants && product.variants.length > 0) {
          // For variable products, calculate total value from variants
          value = product.variants.reduce((sum, variant) => {
            const variantPrice = variant.effective_price || variant.price || 0;
            const variantQuantity = variant.stock_quantity || 0;
            return sum + (variantPrice * variantQuantity);
          }, 0);
          
          // Total stock across all variants
          stock = product.variants.reduce((sum, variant) => sum + (variant.stock_quantity || 0), 0);
          
          // Calculate margin for variable product
          const totalCost = product.variants.reduce((sum, variant) => {
            const variantCost = (product.base_cost || 0) + (product.packaging_cost || 0);
            const variantQuantity = variant.stock_quantity || 0;
            return sum + (variantCost * variantQuantity);
          }, 0);
          
          const totalRevenue = value;
          const profit = totalRevenue - totalCost;
          productMargin = totalCost > 0 ? (profit / totalCost) * 100 : 0;
        } else {
          // For simple products, use existing logic
          value = (product.effective_price || product.price || 0) * (product.stock_quantity || 0);
          stock = product.stock_quantity || 0;
          productMargin = product.profit_margin || 0;
        }
        
        return {
          name: product.name,
          value,
          stock,
          margin: productMargin
        };
      })
      .sort((a, b) => b.value - a.value)
      .slice(0, 5);
    
    // Stock status distribution
    const stockStatusData = [
      { status: 'In Stock', count: totalProducts - lowStockProducts - outOfStockProducts, color: '#10B981' },
      { status: 'Low Stock', count: lowStockProducts, color: '#F59E0B' },
      { status: 'Out of Stock', count: outOfStockProducts, color: '#EF4444' }
    ]
    
    // Count variable vs simple products
    const variableProductsCount = products.filter(p => p.has_variants).length
    const simpleProductsCount = products.length - variableProductsCount
    
    // Count total variants
    const totalVariants = products.reduce((sum, product) => {
      if (product.has_variants && product.variants) {
        return sum + product.variants.length
      }
      return sum
    }, 0)
    
    // Average variants per variable product
    const avgVariantsPerProduct = variableProductsCount > 0 ? totalVariants / variableProductsCount : 0
    
    return {
      totalProducts,
      totalInventoryValue,
      lowStockProducts,
      outOfStockProducts,
      categoryBreakdown,
      topProducts,
      stockStatusData,
      // Margin analysis data
      totalRevenue,
      totalMargin,
      totalCost,
      averageMargin,
      marginDistribution,
      topMarginProducts,
      lowMarginProducts,
      // Variant-specific data
      variableProductsCount,
      simpleProductsCount,
      totalVariants,
      avgVariantsPerProduct
    }
  }, [products])

  // Generate quick insights
  const quickInsights = useMemo(() => {
    const insights = []
    
    if (analyticsData.totalProducts > 0) {
      const avgProductValue = analyticsData.totalInventoryValue / analyticsData.totalProducts
      insights.push({
        label: 'Average Product Value',
        value: formatCurrency(avgProductValue),
        trend: 'neutral' as const
      })
    }
    
    if (analyticsData.totalProducts > 0) {
      const stockOutPercentage = (analyticsData.outOfStockProducts / analyticsData.totalProducts) * 100
      insights.push({
        label: 'Out of Stock Rate',
        value: `${stockOutPercentage.toFixed(1)}%`,
        trend: stockOutPercentage > 10 ? 'up' : 'down' as const
      })
    }
    
    if (analyticsData.categoryBreakdown.length > 0) {
      const topCategory = analyticsData.categoryBreakdown[0]
      insights.push({
        label: 'Top Category',
        value: `${topCategory.category} (${topCategory.percentage.toFixed(1)}%)`,
        trend: 'neutral' as const
      })
    }
    
    // Add variant-specific insights
    if (analyticsData.variableProductsCount > 0) {
      insights.push({
        label: 'Variable Products',
        value: `${analyticsData.variableProductsCount} (${(analyticsData.variableProductsCount / analyticsData.totalProducts * 100).toFixed(1)}%)`,
        trend: 'neutral' as const
      })
      
      if (analyticsData.avgVariantsPerProduct > 0) {
        insights.push({
          label: 'Avg. Variants per Product',
          value: analyticsData.avgVariantsPerProduct.toFixed(1),
          trend: 'neutral' as const
        })
      }
    }
    
    return insights
  }, [analyticsData, formatCurrency])

  // Export functions
  const exportToCSV = useCallback(() => {
    setIsExporting(true)
    
    try {
      const currentDate = new Date().toISOString().split('T')[0]
      const periodLabel = selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)
      
      // Create comprehensive analytics CSV
      const lines = [
        '# Product Analytics Report',
        `# Generated on: ${new Date().toISOString()}`,
        `# Period Focus: ${periodLabel}`,
        '#',
        '# Summary Data:',
        'Metric,Value',
        `Total Products,${analyticsData.totalProducts}`,
        `Total Inventory Value,${analyticsData.totalInventoryValue.toFixed(2)}`,
        `Low Stock Products,${analyticsData.lowStockProducts}`,
        `Out of Stock Products,${analyticsData.outOfStockProducts}`,
        `Variable Products,${analyticsData.variableProductsCount}`,
        `Simple Products,${analyticsData.simpleProductsCount}`,
        `Total Variants,${analyticsData.totalVariants}`,
        `Avg. Variants per Variable Product,${analyticsData.avgVariantsPerProduct.toFixed(2)}`,
        '#',
        '# Category Breakdown:',
        'Category,Count,Value,Percentage',
        ...analyticsData.categoryBreakdown.map(cat => 
          `"${cat.category}",${cat.count},${cat.value.toFixed(2)},${cat.percentage.toFixed(2)}%`
        ),
        '#',
        '# Top Products:',
        'Product,Value,Stock,Margin %',
        ...analyticsData.topProducts.map(prod => 
          `"${prod.name}",${prod.value.toFixed(2)},${prod.stock},${prod.margin.toFixed(2)}%`
        )
      ]
      
      const csvContent = lines.join('\n')
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `product-analytics-${periodLabel.toLowerCase()}-${currentDate}.csv`
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast({
        title: "Analytics Export Complete",
        description: `${periodLabel} analytics report has been downloaded as CSV.`,
      })
    } catch (error) {
      console.error('Analytics CSV export error:', error)
      toast({
        title: "Export Failed",
        description: "Failed to export analytics. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }, [analyticsData, selectedPeriod, toast])

  const exportToPDF = useCallback(() => {
    setIsExporting(true)
    
    try {
      // Dynamic import to avoid SSR issues
      import('jspdf').then(({ default: jsPDF }) => {
        import('jspdf-autotable').then(() => {
          try {
            const doc = new jsPDF()
            const currentDate = new Date().toLocaleDateString()
            const periodLabel = selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)
            
            // Set document properties
            doc.setProperties({
              title: `Product Analytics Report - ${periodLabel}`,
              subject: 'Business Product Analytics',
              creator: 'ONKO Product Management'
            })
            
            // Add header
            doc.setFontSize(20)
            doc.text('Product Analytics Report', 20, 25)
            
            doc.setFontSize(12)
            doc.text(`Generated on: ${currentDate}`, 20, 35)
            doc.text(`Period Focus: ${periodLabel}`, 20, 45)
            
            // Summary section
            doc.setFontSize(14)
            doc.text('Summary', 20, 60)
            
            const summaryData = [
              ['Total Products', analyticsData.totalProducts.toString()],
              ['Total Inventory Value', formatCurrency(analyticsData.totalInventoryValue)],
              ['Low Stock Products', analyticsData.lowStockProducts.toString()],
              ['Out of Stock Products', analyticsData.outOfStockProducts.toString()]
            ]
            
            ;(doc as any).autoTable({
              head: [['Metric', 'Value']],
              body: summaryData,
              startY: 65,
              styles: { fontSize: 10 },
              headStyles: { fillColor: [59, 130, 246] }
            })
            
            // Category breakdown
            const finalY = (doc as any).lastAutoTable.finalY || 65
            doc.setFontSize(14)
            doc.text('Category Breakdown', 20, finalY + 20)
            
            const categoryData = analyticsData.categoryBreakdown.map(cat => [
              cat.category,
              cat.count.toString(),
              formatCurrency(cat.value),
              `${cat.percentage.toFixed(1)}%`
            ])
            
            ;(doc as any).autoTable({
              head: [['Category', 'Count', 'Value', 'Percentage']],
              body: categoryData,
              startY: finalY + 25,
              styles: { fontSize: 10 },
              headStyles: { fillColor: [59, 130, 246] }
            })
            
            // Save PDF
            doc.save(`product-analytics-${periodLabel.toLowerCase()}-${new Date().toISOString().split('T')[0]}.pdf`)
            
            toast({
              title: "Analytics PDF Export Complete",
              description: `${periodLabel} analytics report has been downloaded as PDF.`,
            })
          } catch (error) {
            console.error('Analytics PDF export error:', error)
            toast({
              title: "PDF Export Failed",
              description: "Failed to generate analytics PDF. Please try again.",
              variant: "destructive",
            })
          } finally {
            setIsExporting(false)
          }
        })
      })
    } catch (error) {
      console.error('PDF library loading error:', error)
      toast({
        title: "PDF Export Failed",
        description: "Failed to load PDF library. Please try again.",
        variant: "destructive",
      })
      setIsExporting(false)
    }
  }, [analyticsData, selectedPeriod, formatCurrency, toast])

  if (isLoading) {
    const periodLabel = PERIOD_OPTIONS.find(option => option.value === selectedPeriod)?.label || 'Selected Period'
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center space-y-2">
            <div className="text-muted-foreground">Loading {periodLabel.toLowerCase()} analytics...</div>
            <div className="text-xs text-muted-foreground">Processing product data and calculations</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header with Controls */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Product Analytics</h2>
            <p className="text-muted-foreground">
              Real-time insights into your product inventory and performance
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Period Selection */}
            <div className="flex items-center space-x-1 bg-muted rounded-lg p-1">
              {PERIOD_OPTIONS.map((option) => {
                const Icon = option.icon
                return (
                  <Button
                    key={option.value}
                    // variant={selectedPeriod === option.value ? "default" : "ghost"}
                    // size="sm"
                    onClick={() => setSelectedPeriod(option.value)}
                    className="relative"
                  >
                    <Icon className="h-4 w-4 mr-1" />
                    {option.label}
                  </Button>
                )
              })}
            </div>
            
            {/* Export Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  // variant="outline"
                  // size="sm"
                  disabled={isExporting || products.length === 0}
                  className="flex items-center space-x-1"
                >
                  <Download className="h-4 w-4" />
                  <span>Export</span>
                  <ChevronDown className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={exportToCSV} disabled={isExporting}>
                  <FileSpreadsheet className="h-4 w-4 mr-2" />
                  Export as CSV
                </DropdownMenuItem>
                <DropdownMenuItem onClick={exportToPDF} disabled={isExporting}>
                  <FileText className="h-4 w-4 mr-2" />
                  Export as PDF
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total Products */}
          <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-primary">
                Total Products
              </CardTitle>
              <Package className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">
                {analyticsData.totalProducts}
              </div>
              <p className="text-xs text-primary">
                Active in inventory
              </p>
            </CardContent>
          </Card>

          {/* Inventory Value */}
          <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-primary">
                Inventory Value
              </CardTitle>
              <DollarSign className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">
                {formatCurrency(analyticsData.totalInventoryValue)}
              </div>
              <p className="text-xs text-primary">
                Total asset value
              </p>
            </CardContent>
          </Card>

          {/* Low Stock */}
          <Card className="bg-gradient-to-br from-yellow-50 to-amber-50 border-yellow-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-primary">
                Low Stock
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">
                {analyticsData.lowStockProducts}
              </div>
              <p className="text-xs text-primary">
                Need restocking
              </p>
            </CardContent>
          </Card>

          {/* Out of Stock */}
          <Card className="bg-gradient-to-br from-red-50 to-rose-50 border-red-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-primary">
                Out of Stock
              </CardTitle>
              <X className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-primary">
                {analyticsData.outOfStockProducts}
              </div>
              <p className="text-xs text-primary">
                Unavailable for sale
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Category Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>Category Breakdown</CardTitle>
              <CardDescription>
                Distribution of products by category
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analyticsData.categoryBreakdown.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <div>No products recorded</div>
                  <div className="text-xs mt-1">Add some products to see category breakdown</div>
                </div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Pie Chart */}
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={analyticsData.categoryBreakdown}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={100}
                          dataKey="value"
                          nameKey="category"
                        >
                          {analyticsData.categoryBreakdown.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip 
                          content={({ active, payload }) => {
                            if (active && payload && payload.length) {
                              const data = payload[0].payload
                              return (
                                <div className="bg-background border rounded-lg shadow-md p-3">
                                  <p className="font-medium">{data.category}</p>
                                  <p className="text-primary">
                                    {formatCurrency(data.value)} ({data.percentage.toFixed(1)}%)
                                  </p>
                                  <p className="text-muted-foreground text-sm">
                                    {data.count} products
                                  </p>
                                </div>
                              )
                            }
                            return null
                          }}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  
                  {/* Category List */}
                  <div className="space-y-3">
                    {analyticsData.categoryBreakdown.map((category, index) => (
                      <div key={category.category} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: category.color }}
                          />
                          <span className="font-medium">{category.category}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold">{formatCurrency(category.value)}</div>
                          <div className="text-xs text-muted-foreground">
                            {category.count} products ({category.percentage.toFixed(1)}%)
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Stock Status Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Stock Status Distribution</CardTitle>
              <CardDescription>
                Current inventory availability
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analyticsData.stockStatusData.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <div>No stock data available</div>
                  <div className="text-xs mt-1">Add products to see stock distribution</div>
                </div>
              ) : (
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={analyticsData.stockStatusData}>
                      <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                      <XAxis 
                        dataKey="status" 
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                      />
                      <YAxis
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                      />
                      <Tooltip 
                        content={({ active, payload }) => {
                          if (active && payload && payload.length) {
                            const data = payload[0].payload
                            return (
                              <div className="bg-background border rounded-lg shadow-md p-3">
                                <p className="font-medium">{data.status}</p>
                                <p className="text-blue-600">
                                  {data.count} products
                                </p>
                              </div>
                            )
                          }
                          return null
                        }}
                      />
                      <Bar 
                        dataKey="count" 
                        name="Products"
                      >
                        {analyticsData.stockStatusData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Bottom Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Products by Value */}
          <Card>
            <CardHeader>
              <CardTitle>Top Products by Value</CardTitle>
              <CardDescription>
                Highest inventory value products
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analyticsData.topProducts.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <div>No products recorded</div>
                  <div className="text-xs mt-1">Add some products to see top performers</div>
                </div>
              ) : (
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={analyticsData.topProducts}>
                      <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                      <XAxis 
                        dataKey="name" 
                        fontSize={10}
                        tickLine={false}
                        axisLine={false}
                        angle={-45}
                        textAnchor="end"
                        height={60}
                      />
                      <YAxis
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                        tickFormatter={(value) => `$${value}`}
                      />
                      <Tooltip 
                        content={({ active, payload }) => {
                          if (active && payload && payload.length) {
                            const data = payload[0].payload
                            return (
                              <div className="bg-background border rounded-lg shadow-md p-3">
                                <p className="font-medium">{data.name}</p>
                                <p className="text-blue-600">
                                  Value: {formatCurrency(data.value)}
                                </p>
                                <p className="text-muted-foreground text-sm">
                                  Stock: {data.stock} units
                                </p>
                                <p className="text-muted-foreground text-sm">
                                  Margin: {data.margin.toFixed(1)}%
                                </p>
                              </div>
                            )
                          }
                          return null
                        }}
                      />
                      <Bar 
                        dataKey="value" 
                        name="Value"
                        fill="#3B82F6"
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Quick Insights */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Insights</CardTitle>
              <CardDescription>
                Key statistics and trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {quickInsights.map((insight, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">{insight.label}</span>
                    <span className={cn(
                      "font-medium",
                      insight.trend === 'up' && "text-red-600",
                      insight.trend === 'down' && "text-green-600",
                      insight.trend === 'neutral' && "text-muted-foreground"
                    )}>
                      {insight.value}
                    </span>
                  </div>
                ))}
                {quickInsights.length === 0 && (
                  <div className="text-center py-4 text-muted-foreground">
                    <div>No insights available</div>
                    <div className="text-xs mt-1">Add products to generate insights</div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Margin Analysis Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
          {/* Overall Margin Metrics */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle>Margin Overview</CardTitle>
              <CardDescription>
                Overall profitability metrics
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-50 p-3 rounded-lg">
                  <div className="text-sm text-blue-800">Total Revenue</div>
                  <div className="text-lg font-semibold text-blue-900">
                    {formatCurrency(analyticsData.totalRevenue)}
                  </div>
                </div>
                <div className="bg-green-50 p-3 rounded-lg">
                  <div className="text-sm text-green-800">Total Profit</div>
                  <div className={cn(
                    "text-lg font-semibold",
                    analyticsData.totalMargin < 0 ? "text-red-600" : "text-green-900"
                  )}>
                    {formatCurrency(analyticsData.totalMargin)}
                  </div>
                </div>
                <div className="bg-purple-50 p-3 rounded-lg">
                  <div className="text-sm text-purple-800">Avg. Margin</div>
                  <div className={cn(
                    "text-lg font-semibold",
                    analyticsData.averageMargin < 0 ? "text-red-600" : 
                    analyticsData.averageMargin < 15 ? "text-yellow-600" :
                    analyticsData.averageMargin < 30 ? "text-green-600" : "text-primary"
                  )}>
                    {analyticsData.averageMargin.toFixed(1)}%
                  </div>
                </div>
                <div className="bg-orange-50 p-3 rounded-lg">
                  <div className="text-sm text-orange-800">Total Cost</div>
                  <div className="text-lg font-semibold text-orange-900">
                    {formatCurrency(analyticsData.totalCost)}
                  </div>
                </div>
              </div>
              
              <div className="pt-2 border-t">
                <div className="text-sm text-muted-foreground mb-2">
                  Margin Health
                </div>
                <div className="flex items-center space-x-2">
                  <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div 
                      className={cn(
                        "h-full",
                        analyticsData.averageMargin < 0 ? "bg-red-500" : 
                        analyticsData.averageMargin < 15 ? "bg-yellow-500" :
                        analyticsData.averageMargin < 30 ? "bg-green-500" : "bg-primary"
                      )}
                      style={{ width: `${Math.min(100, Math.max(0, analyticsData.averageMargin))}%` }}
                    />
                  </div>
                  <span className="text-xs font-medium">
                    {analyticsData.averageMargin.toFixed(1)}%
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Margin Distribution */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Margin Distribution</CardTitle>
              <CardDescription>
                Profitability ranges across products
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analyticsData.marginDistribution.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <div>No margin data available</div>
                </div>
              ) : (
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={analyticsData.marginDistribution}>
                      <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                      <XAxis 
                        dataKey="range" 
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                      />
                      <YAxis
                        fontSize={12}
                        tickLine={false}
                        axisLine={false}
                      />
                      <Tooltip 
                        content={({ active, payload }) => {
                          if (active && payload && payload.length) {
                            const data = payload[0].payload
                            return (
                              <div className="bg-background border rounded-lg shadow-md p-3">
                                <p className="font-medium">{data.range} Margin</p>
                                <p className="text-blue-600">
                                  {data.count} products
                                </p>
                              </div>
                            )
                          }
                          return null
                        }}
                      />
                      <Bar 
                        dataKey="count" 
                        name="Products"
                      >
                        {analyticsData.marginDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Top and Low Margin Products */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
          {/* Top Margin Products */}
          <Card>
            <CardHeader>
              <CardTitle>Top Margin Products</CardTitle>
              <CardDescription>
                Products with highest profitability
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analyticsData.topMarginProducts.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  <div>No products with positive margins</div>
                </div>
              ) : (
                <div className="space-y-3">
                  {analyticsData.topMarginProducts.slice(0, 5).map((product, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="font-medium text-sm">{product.name}</div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-green-600">
                          {product.margin.toFixed(1)}%
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {formatCurrency(product.value)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Low Margin Products */}
          <Card>
            <CardHeader>
              <CardTitle>Low Margin Products</CardTitle>
              <CardDescription>
                Products with lowest profitability
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analyticsData.lowMarginProducts.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  <div>All products have healthy margins</div>
                </div>
              ) : (
                <div className="space-y-3">
                  {analyticsData.lowMarginProducts.slice(0, 5).map((product, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="font-medium text-sm">{product.name}</div>
                      </div>
                      <div className="text-right">
                        <div className={cn(
                          "font-semibold",
                          product.margin < 0 ? "text-red-600" : "text-yellow-600"
                        )}>
                          {product.margin.toFixed(1)}%
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {formatCurrency(product.value)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}