'use client'

import { useState, useEffect, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { DatePicker } from '@/components/ui/date-picker'
import { useToast } from '@/components/ui/use-toast'
import { CustomDropdown, type CustomDropdownOption } from '@/components/ui/custom-dropdown'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { createSupabaseClient, type ProductRow, type ProductVariantRow, type CategoryRow } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { 
  Package, 
  RefreshCw, 
  ChevronDown, 
  ChevronUp,
  Info,
  Plus,
  X
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface EditProductFormProps {
  product: ProductRow
  open: boolean
  onOpenChange: (open: boolean) => void
  onProductUpdated?: () => void
}

interface ProductFormData {
  // Product Type
  has_variants: boolean
  
  // Basic Information
  name: string
  description: string
  brand: string
  supplier: string
  base_sku: string
  category_id: string
  
  // Simple Product Fields (when has_variants = false)
  size: string
  color: string
  base_cost: number
  packaging_cost: number
  price: number | null
  stock_quantity: number
  low_stock_threshold: number
  barcode: string
  
  // Variable Product Attributes (when has_variants = true)
  variant_attributes: {
    name: string;
    values: string[];
  }[]
  
  // Sale/Discount Fields
  sale_price: number | null
  sale_start_date: Date | null
  sale_end_date: Date | null
  
  // Additional Fields
  batch_reference: string
  purchase_date: Date | null
  notes: string
}

interface VariantData {
  id?: string
  sku: string
  price: number | null
  stock_quantity: number
  low_stock_threshold: number
  size?: string | null
  color?: string | null
  material?: string | null
  style?: string | null
}

const INITIAL_FORM_DATA: ProductFormData = {
  has_variants: false,
  name: '',
  description: '',
  brand: '',
  supplier: '',
  base_sku: '',
  category_id: '',
  size: '',
  color: '',
  base_cost: 0,
  packaging_cost: 0,
  price: null,
  stock_quantity: 0,
  low_stock_threshold: 10,
  barcode: '',
  variant_attributes: [],
  sale_price: null,
  sale_start_date: null,
  sale_end_date: null,
  batch_reference: '',
  purchase_date: null,
  notes: ''
}

export function EditProductForm({ product, open, onOpenChange, onProductUpdated }: EditProductFormProps) {
  const [formData, setFormData] = useState<ProductFormData>(INITIAL_FORM_DATA)
  const [categories, setCategories] = useState<CategoryRow[]>([])
  const [categoryOptions, setCategoryOptions] = useState<CustomDropdownOption[]>([])
  const [suppliers, setSuppliers] = useState<string[]>([])
  const [isSaleExpanded, setIsSaleExpanded] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [variants, setVariants] = useState<VariantData[]>([])
  const [existingVariants, setExistingVariants] = useState<ProductVariantRow[]>([])
  const [editingVariants, setEditingVariants] = useState<Record<string, boolean>>({})
  const [variantUpdates, setVariantUpdates] = useState<Record<string, Partial<ProductVariantRow>>>({})
  
  // Mobile responsive states
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    'product-info': true,
    'product-details': true,
    'supplier': true,
    'pricing': true,
    'inventory': true,
    'additional': true
  })
  
  const { toast } = useToast()
  const { user } = useAuth()
  const { formatCurrency } = useCurrency()
  const supabase = createSupabaseClient()

  // Initialize form data when product changes
  useEffect(() => {
    if (product && open) {
      initializeFormData()
      loadCategories()
      loadSuppliers()
      if (product.has_variants) {
        loadExistingVariants()
      }
    }
  }, [product, open])

  const initializeFormData = () => {
    // Extract attributes from existing variants
    let variantAttributes: { name: string; values: string[] }[] = [];
    
    if (product.has_variants && existingVariants.length > 0) {
      // Extract unique attribute names and values from existing variants
      const attributeMap: { [key: string]: Set<string> } = {};
      
      // Collect all attribute values from existing variants
      existingVariants.forEach(variant => {
        if (variant.size) {
          if (!attributeMap['Size']) attributeMap['Size'] = new Set();
          attributeMap['Size'].add(variant.size);
        }
        if (variant.color) {
          if (!attributeMap['Color']) attributeMap['Color'] = new Set();
          attributeMap['Color'].add(variant.color);
        }
        if (variant.material) {
          if (!attributeMap['Material']) attributeMap['Material'] = new Set();
          attributeMap['Material'].add(variant.material);
        }
        if (variant.style) {
          if (!attributeMap['Style']) attributeMap['Style'] = new Set();
          attributeMap['Style'].add(variant.style);
        }
      });
      
      // Convert to the format we need
      variantAttributes = Object.entries(attributeMap).map(([name, values]) => ({
        name,
        values: Array.from(values)
      }));
    }
    
    setFormData({
      has_variants: product.has_variants || false,
      name: product.name || '',
      description: product.description || '',
      brand: product.brand || '',
      supplier: product.supplier || '',
      base_sku: product.base_sku || '',
      category_id: product.category_id || '',
      size: product.size || '',
      color: product.color || '',
      base_cost: product.base_cost || 0,
      packaging_cost: product.packaging_cost || 0,
      price: product.price,
      stock_quantity: product.stock_quantity || 0,
      low_stock_threshold: product.low_stock_threshold || 10,
      barcode: product.barcode || '',
      variant_attributes: variantAttributes,
      sale_price: product.sale_price,
      sale_start_date: product.sale_start_date ? new Date(product.sale_start_date) : null,
      sale_end_date: product.sale_end_date ? new Date(product.sale_end_date) : null,
      batch_reference: product.batch_reference || '',
      purchase_date: product.purchase_date ? new Date(product.purchase_date) : null,
      notes: product.notes || ''
    });
  };

  const loadCategories = async () => {
    if (!user?.id) return
    
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('user_id', user.id)
        .order('name')

      if (error) {
        console.error('Error loading categories:', error)
      } else {
        setCategories(data || [])
      }
    } catch (error) {
      console.error('Error loading categories:', error)
    }
  }

  const loadSuppliers = async () => {
    // Load suppliers from the database
    if (!user?.id) return
    
    try {
      // Get unique suppliers from existing products
      const { data, error } = await supabase
        .from('products')
        .select('supplier')
        .eq('user_id', user.id)
        .not('supplier', 'is', null)
      
      if (error) {
        console.error('Error loading suppliers:', error)
      } else {
        // Extract unique suppliers
        const uniqueSuppliers = Array.from(new Set(data.map(item => item.supplier).filter(Boolean))) as string[]
        setSuppliers(uniqueSuppliers.length > 0 ? uniqueSuppliers : ['Vendor A', 'Vendor B', 'Silver Jewelry', 'Local Supplier'])
      }
    } catch (error) {
      console.error('Error loading suppliers:', error)
      // Fallback to default suppliers
      setSuppliers(['Vendor A', 'Vendor B', 'Silver Jewelry', 'Local Supplier'])
    }
  }

  const loadExistingVariants = async () => {
    if (!product.id) return
    
    try {
      const { data, error } = await supabase
        .from('product_variants')
        .select('*')
        .eq('product_id', product.id)
        .order('created_at')

      if (error) {
        console.error('Error loading variants:', error)
      } else {
        setExistingVariants(data || [])
      }
    } catch (error) {
      console.error('Error loading variants:', error)
    }
  }

  // Update category options when categories change
  useEffect(() => {
    const options = categories.map(category => ({
      value: category.id,
      label: category.name
    }))
    setCategoryOptions(options)
  }, [categories])

  // Real-time calculations
  const calculations = useMemo(() => {
    const totalCost = formData.base_cost + formData.packaging_cost
    const effectivePrice = formData.sale_price || formData.price || 0
    const profitAmount = effectivePrice - totalCost
    const profitMargin = totalCost > 0 ? (profitAmount / totalCost) * 100 : 0
    
    // Color coding for margins
    let marginColor = 'text-gray-600'
    if (profitMargin < 0) marginColor = 'text-red-600'
    else if (profitMargin < 15) marginColor = 'text-yellow-600'
    else if (profitMargin < 30) marginColor = 'text-green-600'
    else marginColor = 'text-primary'

    return {
      totalCost,
      effectivePrice,
      profitAmount,
      profitMargin,
      marginColor,
      isOnSale: !!formData.sale_price
    }
  }, [formData.base_cost, formData.packaging_cost, formData.price, formData.sale_price])

  const updateFormData = (field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleVariantUpdate = async (variantId: string) => {
    if (!user?.id) return

    const updates = variantUpdates[variantId]
    if (!updates) return

    try {
      const { error } = await supabase
        .from('product_variants')
        .update(updates)
        .eq('id', variantId)
        .eq('user_id', user.id)

      if (error) {
        toast({
          title: "Error",
          description: `Failed to update variant: ${error.message}`,
          variant: "destructive",
        })
        return
      }

      // Reload variants
      await loadExistingVariants()

      // Clear editing state
      setEditingVariants(prev => ({ ...prev, [variantId]: false }))
      setVariantUpdates(prev => {
        const newUpdates = { ...prev }
        delete newUpdates[variantId]
        return newUpdates
      })

      toast({
        title: "Variant Updated",
        description: "Variant has been updated successfully.",
      })
    } catch (error) {
      console.error('Error updating variant:', error)
      toast({
        title: "Error",
        description: "Failed to update variant. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Generate all combinations of attribute values
  const generateAttributeCombinations = (attributes: { name: string; values: string[] }[]): { name: string; value: string }[][] => {
    if (attributes.length === 0) return [];
    if (attributes.length === 1) {
      return attributes[0].values.map(value => [{ name: attributes[0].name, value }]);
    }
    
    const firstAttr = attributes[0];
    const restAttrs = attributes.slice(1);
    const restCombinations = generateAttributeCombinations(restAttrs);
    
    const combinations: { name: string; value: string }[][] = [];
    
    firstAttr.values.forEach(value => {
      restCombinations.forEach(combo => {
        combinations.push([{ name: firstAttr.name, value }, ...combo]);
      });
    });
    
    return combinations;
  };

  // Toggle section expansion for mobile
  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }))
  }

  // Handle adding custom category
  const handleAddCustomCategory = async (newCategory: CustomDropdownOption) => {
    if (!user?.id) return
    
    try {
      const { data, error } = await supabase
        .from('categories')
        .insert({
          user_id: user.id,
          name: newCategory.label,
          description: ''
        })
        .select()
        .single()

      if (error) {
        toast({
          title: "Error Creating Category",
          description: `Failed to create category: ${error.message}`,
          variant: "destructive",
        })
        return
      }

      // Add to categories list
      setCategories(prev => [...prev, data])
      
      // Select the new category
      updateFormData('category_id', data.id)
      
      toast({
        title: "Category Created",
        description: `Successfully created category "${newCategory.label}".`,
      })
    } catch (error) {
      console.error('Error creating category:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to create category. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user?.id || !product.id) return

    setIsSubmitting(true)
    
    try {
      // Check if we're converting from simple to variable product
      const isConvertingToVariable = !product.has_variants && formData.has_variants;
      const isConvertingToSimple = product.has_variants && !formData.has_variants;
      
      if (isConvertingToSimple) {
        toast({
          title: "Conversion Not Supported",
          description: "Converting from variable to simple product is not supported in this version.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }
      
      // Update the master product
      const updateData: any = {
        name: formData.name,
        description: formData.description || null,
        brand: formData.brand || null,
        supplier: formData.supplier || null,
        base_sku: formData.base_sku || null,
        category_id: formData.category_id || null,
        base_cost: formData.base_cost || null,
        packaging_cost: formData.packaging_cost,
        sale_price: formData.sale_price,
        sale_start_date: formData.sale_start_date?.toISOString().split('T')[0] || null,
        sale_end_date: formData.sale_end_date?.toISOString().split('T')[0] || null,
        batch_reference: formData.batch_reference || null,
        purchase_date: formData.purchase_date?.toISOString().split('T')[0] || null,
        notes: formData.notes || null,
        updated_at: new Date().toISOString()
      }

      // Handle product type conversion
      if (isConvertingToVariable) {
        // Converting from simple to variable
        updateData.has_variants = true;
        // Clear simple product fields
        updateData.price = null;
        updateData.size = null;
        updateData.color = null;
        updateData.stock_quantity = 0;
        updateData.low_stock_threshold = 10;
        updateData.barcode = null;
      } else if (!formData.has_variants) {
        // Simple product (no conversion)
        updateData.price = formData.price;
        updateData.size = formData.size || null;
        updateData.color = formData.color || null;
        updateData.stock_quantity = formData.stock_quantity;
        updateData.low_stock_threshold = formData.low_stock_threshold;
        updateData.barcode = formData.barcode || null;
      } else {
        // Variable product (no conversion)
        updateData.price = null;
        updateData.size = null;
        updateData.color = null;
        updateData.stock_quantity = 0;
        updateData.low_stock_threshold = 10;
        updateData.barcode = null;
      }

      const { error: updateError } = await supabase
        .from('products')
        .update(updateData)
        .eq('id', product.id)
        .eq('user_id', user.id)

      if (updateError) {
        throw new Error(`Failed to update product: ${updateError.message}`)
      }

      // If converting from simple to variable, create the first variant
      if (isConvertingToVariable) {
        // Create a variant based on the original simple product data
        const variantData: any = {
          product_id: product.id,
          user_id: user.id,
          sku: `${formData.base_sku || 'VAR'}-1`,
          variant_name: 'Default Variant',
          price: formData.price || 0,
          stock_quantity: formData.stock_quantity || 0,
          low_stock_threshold: formData.low_stock_threshold || 10,
          cost_adjustment: 0
        };
        
        // Add size and color if they exist in the original product
        if (product.size) variantData.size = product.size;
        if (product.color) variantData.color = product.color;
        
        const { error: variantError } = await supabase
          .from('product_variants')
          .insert([variantData]);
          
        if (variantError) {
          console.warn('Failed to create initial variant:', variantError);
          // Don't throw error here as the main product update was successful
        }
      }

      // If it's a variable product and attributes have changed, we need to handle variants
      if (formData.has_variants && formData.variant_attributes.length > 0) {
        // For now, we'll just show a message that variant editing is not fully implemented
        // In a full implementation, we would:
        // 1. Compare the new attributes with existing variants
        // 2. Delete variants that no longer match
        // 3. Create new variants based on the new attribute combinations
        toast({
          title: "Product Updated",
          description: "Product information updated successfully. Note: Full variant attribute editing is not fully implemented yet. To modify variants, you would need to delete existing variants and create new ones.",
        })
      } else {
        toast({
          title: "Product Updated",
          description: "Product information updated successfully.",
        })
      }

      // Notify parent component
      onProductUpdated?.()
      onOpenChange(false)
      
      // Dispatch a custom event to trigger table refresh
      window.dispatchEvent(new CustomEvent('productUpdated'))
    } catch (error) {
      console.error('Error updating product:', error)
      toast({
        title: "Error Updating Product",
        description: error instanceof Error ? error.message : "Failed to update product",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Product</DialogTitle>
        </DialogHeader>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <p className="text-xs text-blue-800">
            <strong>Note:</strong> Stock quantities can only be adjusted in the Inventory section. 
            This form is for editing product information only.
          </p>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Product Type Selection */}
          <div className="space-y-2">
            <Label className="text-xs">
              <span>Product Type</span>
            </Label>
            <Select 
              value={formData.has_variants ? 'variable' : 'simple'} 
              onValueChange={(value) => {
                const isVariable = value === 'variable';
                updateFormData('has_variants', isVariable);
                
                // If converting from simple to variable, initialize with current simple product data
                if (isVariable && !product.has_variants) {
                  // Pre-populate with simple product data as initial variant
                  const initialAttributes: { name: string; values: string[] }[] = [];
                  
                  if (product.size) {
                    initialAttributes.push({ name: 'Size', values: [product.size] });
                  }
                  
                  if (product.color) {
                    initialAttributes.push({ name: 'Color', values: [product.color] });
                  }
                  
                  updateFormData('variant_attributes', initialAttributes);
                }
              }}

            >
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="simple">Simple Product</SelectItem>
                <SelectItem value="variable">Variable Product</SelectItem>
              </SelectContent>
            </Select>
            
            {/* Warning when converting product type */}
            {!product.has_variants && formData.has_variants && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2 mt-2">
                <p className="text-xs text-yellow-800">
                  <strong>Conversion Notice:</strong> Converting from simple to variable product will move the current product data to variants. 
                  The main product will become a container for variants.
                </p>
              </div>
            )}
            
            {product.has_variants && !formData.has_variants && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-2 mt-2">
                <p className="text-xs text-red-800">
                  <strong>Conversion Warning:</strong> Converting from variable to simple product is not recommended and may result in data loss. 
                  All variants will be removed.
                </p>
              </div>
            )}
          </div>

          {/* Basic Product Information */}
          <div className="border rounded-lg">
            <Button
              type="button"
              variant="ghost"
              onClick={() => toggleSection('product-info')}
              className="flex items-center justify-between w-full py-3.5 px-3 h-auto bg-gray-50 rounded-t-lg"
            >
              <div className="flex items-center space-x-2">
                <span className="font-medium">Product Information</span>
              </div>
              {expandedSections['product-info'] ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
            
            {expandedSections['product-info'] && (
              <div className="px-3 pt-3 pb-3 space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="space-y-1.5">
                    <Label htmlFor="name" className="text-xs">Product Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => updateFormData('name', e.target.value)}
                      placeholder="Enter product name"
                      required
                      className="h-8 text-xs placeholder:text-xs"
                    />
                  </div>
                  
                  <div className="space-y-1.5">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1">
                        <Label htmlFor="base_sku" className="text-xs">SKU</Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-4 w-4 text-xs">
                              <Info className="h-3 w-3 text-muted-foreground" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="max-w-[300px]" side="right" align="start">
                            <div className="space-y-2">
                              <h4 className="font-semibold">SKU Information</h4>
                              <p className="text-xs text-muted-foreground">This is the unique identifier for your product.</p>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          const namePrefix = formData.name
                            .split(' ')
                            .map(word => word.substring(0, 2).toUpperCase())
                            .join('')
                            .substring(0, 6)
                          
                          const date = new Date().toISOString().slice(0, 10).replace(/-/g, '')
                          const random = Math.floor(Math.random() * 100).toString().padStart(2, '0')
                          
                          const newSKU = `${namePrefix}-${date}-${random}`
                          updateFormData('base_sku', newSKU)
                        }}
                        className="h-6 w-6 p-0 text-xs"
                      >
                        <RefreshCw className="h-3 w-3" />
                      </Button>
                    </div>
                    <Input
                      id="base_sku"
                      value={formData.base_sku}
                      onChange={(e) => updateFormData('base_sku', e.target.value)}
                      placeholder="Auto-generated"
                      className="h-8 text-xs placeholder:text-xs"
                    />
                  </div>
                </div>

                <div className="space-y-1.5">
                  <Label htmlFor="description" className="text-xs">Description</Label>
                  <Input
                    id="description"
                    value={formData.description}
                    onChange={(e) => updateFormData('description', e.target.value)}
                    placeholder="Enter product description"
                    className="h-8 text-xs placeholder:text-xs"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="border rounded-lg">
            <Button
              type="button"
              variant="ghost"
              onClick={() => toggleSection('product-details')}
              className="flex items-center justify-between w-full py-3.5 px-3 h-auto bg-gray-50 rounded-t-lg"
            >
              <div className="flex items-center space-x-2">
                <span className="font-medium">Product Details</span>
              </div>
              {expandedSections['product-details'] ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
            
            {expandedSections['product-details'] && (
              <div className="px-3 pt-3 pb-3 space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div className="space-y-1.5">
                    <Label htmlFor="category_id" className="text-xs">Category *</Label>
                    <CustomDropdown
                      options={categoryOptions}
                      value={formData.category_id}
                      onValueChange={(value) => updateFormData('category_id', value)}
                      onAddCustomOption={handleAddCustomCategory}
                      placeholder="Select category"
                      allowCustom={true}
                      customPlaceholder="Add new category"
                      className="h-8 text-xs placeholder:text-xs"
                    />
                  </div>

                  {!formData.has_variants && (
                    <>
                      <div className="space-y-1.5">
                        <Label htmlFor="size" className="text-xs">Size</Label>
                        <Input
                          id="size"
                          value={formData.size}
                          onChange={(e) => updateFormData('size', e.target.value)}
                          placeholder="Enter size"
                          className="h-8 text-xs placeholder:text-xs"
                        />
                      </div>
                      
                      <div className="space-y-1.5">
                        <Label htmlFor="color" className="text-xs">Color</Label>
                        <Input
                          id="color"
                          value={formData.color}
                          onChange={(e) => updateFormData('color', e.target.value)}
                          placeholder="Enter color"
                          className="h-8 text-xs placeholder:text-xs"
                        />
                      </div>
                    </>
                  )}
                </div>

                {/* Quantity for simple products */}
                {!formData.has_variants && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div className="space-y-1.5">
                      <div className="flex items-center gap-1">
                        <Label htmlFor="stock_quantity" className="text-xs">Quantity *</Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-4 w-4 text-xs">
                              <Info className="h-3 w-3 text-muted-foreground" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="max-w-[300px]" side="right" align="start">
                            <div className="space-y-2">
                              <h4 className="font-semibold">Stock Quantity</h4>
                              <p className="text-xs text-muted-foreground">Stock quantity can only be adjusted in the Inventory section</p>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                      <Input
                        id="stock_quantity"
                        type="number"
                        value={formData.stock_quantity}
                        onChange={(e) => updateFormData('stock_quantity', parseInt(e.target.value) || 0)}
                        placeholder="Enter quantity"
                        required
                        className="h-8 text-xs placeholder:text-xs"
                        disabled
                      />
                    </div>
                    
                    <div className="space-y-1.5">
                      <div className="flex items-center h-[21px]">  {/* Added fixed height to match the Quantity label with popover */}
                        <Label htmlFor="low_stock_threshold" className="text-xs">Low Stock Alert</Label>
                      </div>
                      <Input
                        id="low_stock_threshold"
                        type="number"
                        value={formData.low_stock_threshold}
                        onChange={(e) => updateFormData('low_stock_threshold', parseInt(e.target.value) || 10)}
                        placeholder="Enter low stock threshold"
                        className="h-8 text-xs placeholder:text-xs"
                      />
                    </div>
                    
                    <div className="space-y-1.5">
                      <div className="flex items-center h-[21px]">  {/* Added fixed height to match the Quantity label with popover */}
                        <Label htmlFor="barcode" className="text-xs">Barcode</Label>
                      </div>
                      <Input
                        id="barcode"
                        value={formData.barcode}
                        onChange={(e) => updateFormData('barcode', e.target.value)}
                        placeholder="Enter barcode"
                        className="h-8 text-xs placeholder:text-xs"
                      />
                    </div>
                  </div>
                )}

                {/* Variable Product Attributes */}
                {formData.has_variants && (
                  <div className="space-y-4 pt-2 border-t">
                    <h4 className="font-medium text-sm">Variable Product Attributes</h4>
                    
                    {/* Attribute Management */}
                    <div className="space-y-3">
                      {formData.variant_attributes.map((attr, index) => (
                        <div key={index} className="flex items-start gap-2">
                          <div className="flex-1 space-y-1.5">
                            <div className="h-5 flex items-center">
                              <Label className="text-xs">Attribute Name</Label>
                            </div>
                            <Select
                              value={attr.name || "custom"}
                              onValueChange={(value) => {
                                const newAttributes = [...formData.variant_attributes];
                                newAttributes[index].name = value === "custom" ? "" : value;
                                updateFormData("variant_attributes", newAttributes);
                              }}
                            >
                              <SelectTrigger className="h-8 text-xs">
                                <SelectValue placeholder="Select attribute" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Size">Size</SelectItem>
                                <SelectItem value="Color">Color</SelectItem>
                                <SelectItem value="Material">Material</SelectItem>
                                <SelectItem value="Style">Style</SelectItem>
                                <SelectItem value="custom">Custom Attribute...</SelectItem>
                              </SelectContent>
                            </Select>
                            {attr.name === "" && (
                              <Input
                                value={attr.name}
                                onChange={(e) => {
                                  const newAttributes = [...formData.variant_attributes];
                                  newAttributes[index].name = e.target.value;
                                  updateFormData("variant_attributes", newAttributes);
                                }}
                                placeholder="Enter custom attribute name"
                                className="h-8 text-xs placeholder:text-xs mt-2"
                              />
                            )}
                          </div>
                          <div className="flex-1 space-y-1.5">
                            <div className="h-5 flex items-center">
                              <Label className="text-xs">Values (comma separated)</Label>
                            </div>
                            <Input
                              value={attr.values.join(", ")}
                              onChange={(e) => {
                                const newAttributes = [...formData.variant_attributes];
                                newAttributes[index].values = e.target.value
                                  .split(",")
                                  .map((v) => v.trim())
                                  .filter((v) => v !== "");
                                updateFormData("variant_attributes", newAttributes);
                              }}
                              placeholder="e.g., Small, Medium, Large"
                              className="h-8 text-xs placeholder:text-xs"
                            />
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              const newAttributes = [...formData.variant_attributes];
                              newAttributes.splice(index, 1);
                              updateFormData("variant_attributes", newAttributes);
                            }}
                            className="h-8 w-8 p-0 mt-5"
                          >
                            <span className="sr-only">Remove</span>
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          updateFormData("variant_attributes", [
                            ...formData.variant_attributes,
                            { name: "", values: [] },
                          ]);
                        }}
                        className="w-full h-8 text-xs"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Attribute
                      </Button>
                    </div>
                    
                    {/* Variant Matrix Preview */}
                    {formData.variant_attributes.length > 0 && 
                     formData.variant_attributes.every(attr => attr.name && attr.values.length > 0) && (
                      <div className="mt-4 space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-sm">Variant Matrix Preview</h4>
                          <Button 
                            type="button" 
                            variant="outline" 
                            size="sm"
                            className="h-8 text-xs"
                          >
                            <RefreshCw className="h-3 w-3 mr-1" />
                            Refresh Variants
                          </Button>
                        </div>
                        
                        <div className="border rounded-md overflow-x-auto">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                {formData.variant_attributes.map((attr, idx) => (
                                  <TableHead key={idx} className="whitespace-nowrap text-xs">{attr.name}</TableHead>
                                ))}
                                <TableHead className="whitespace-nowrap text-xs">SKU</TableHead>
                                <TableHead className="whitespace-nowrap text-xs">Price</TableHead>
                                <TableHead className="whitespace-nowrap text-xs">Quantity</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {generateAttributeCombinations(formData.variant_attributes).map((combo, idx) => {
                                // Generate SKU for this variant
                                const skuSuffix = combo.map(item => item.value.substring(0, 3).toUpperCase()).join('-');
                                const defaultSku = `${formData.base_sku || formData.name?.substring(0, 6).toUpperCase() || 'PROD'}-${skuSuffix}`;
                                
                                return (
                                  <TableRow key={idx}>
                                    {combo.map((item, itemIdx) => (
                                      <TableCell key={itemIdx} className="whitespace-nowrap text-xs">{item.value}</TableCell>
                                    ))}
                                    <TableCell className="font-mono text-xs">
                                      {defaultSku}
                                    </TableCell>
                                    <TableCell>
                                      <Input 
                                        type="number" 
                                        placeholder="0.00" 
                                        className="h-8 w-20 text-xs placeholder:text-xs" 
                                        defaultValue={formData.price || ''}
                                      />
                                    </TableCell>
                                    <TableCell>
                                      <Input 
                                        type="number" 
                                        placeholder="0" 
                                        className="h-8 w-16 text-xs placeholder:text-xs"
                                        disabled
                                      />
                                      <p className="text-xs text-muted-foreground mt-1">
                                        Set in Inventory
                                      </p>
                                    </TableCell>
                                  </TableRow>
                                );
                              })}
                            </TableBody>
                          </Table>
                        </div>
                        
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                          <p className="text-xs text-muted-foreground">
                            {generateAttributeCombinations(formData.variant_attributes).length} variants will be created
                          </p>
                          <div className="flex flex-wrap gap-2">
                            <Button 
                              type="button" 
                              variant="outline" 
                              size="sm"
                              className="h-8 text-xs"
                            >
                              Set All Prices
                            </Button>
                            <Button 
                              type="button" 
                              variant="outline" 
                              size="sm"
                              disabled
                              className="h-8 text-xs"
                            >
                              Set All Quantities
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {existingVariants.length > 0 && (
                      <div className="pt-4 border-t">
                        <h4 className="font-medium text-sm mb-2">Existing Variants</h4>
                        <p className="text-xs text-muted-foreground mb-2">
                          Click on any field to edit. Stock quantities can be adjusted in the Inventory section.
                        </p>
                        <div className="border rounded-md overflow-x-auto">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead className="text-xs">Variant</TableHead>
                                <TableHead className="text-xs">SKU</TableHead>
                                <TableHead className="text-xs">Base Cost</TableHead>
                                <TableHead className="text-xs">Price</TableHead>
                                <TableHead className="text-xs">Stock</TableHead>
                                <TableHead className="text-xs">Actions</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {existingVariants.map((variant) => {
                                const isEditing = editingVariants[variant.id]
                                const updates = variantUpdates[variant.id] || {}

                                return (
                                <TableRow key={variant.id}>
                                  <TableCell className="font-medium text-xs">
                                    {variant.variant_name || 'Unnamed Variant'}
                                  </TableCell>
                                  <TableCell className="font-mono text-xs">
                                    {isEditing ? (
                                      <Input
                                        value={updates.sku ?? variant.sku}
                                        onChange={(e) => setVariantUpdates(prev => ({
                                          ...prev,
                                          [variant.id]: { ...updates, sku: e.target.value }
                                        }))}
                                        className="h-7 text-xs"
                                      />
                                    ) : (
                                      <span
                                        className="cursor-pointer hover:bg-gray-100 px-1 py-0.5 rounded"
                                        onClick={() => setEditingVariants(prev => ({ ...prev, [variant.id]: true }))}
                                      >
                                        {variant.sku}
                                      </span>
                                    )}
                                  </TableCell>
                                  <TableCell className="text-xs">
                                    {isEditing ? (
                                      <Input
                                        type="number"
                                        step="0.01"
                                        value={updates.base_cost ?? variant.base_cost ?? variant.cost_adjustment ?? 0}
                                        onChange={(e) => setVariantUpdates(prev => ({
                                          ...prev,
                                          [variant.id]: { ...updates, base_cost: parseFloat(e.target.value) || 0 }
                                        }))}
                                        className="h-7 text-xs"
                                      />
                                    ) : (
                                      <span
                                        className="cursor-pointer hover:bg-gray-100 px-1 py-0.5 rounded"
                                        onClick={() => setEditingVariants(prev => ({ ...prev, [variant.id]: true }))}
                                      >
                                        {formatCurrency((variant.base_cost && variant.base_cost > 0) ? variant.base_cost : (variant.cost_adjustment || 0))}
                                      </span>
                                    )}
                                  </TableCell>
                                  <TableCell className="text-xs">
                                    {isEditing ? (
                                      <Input
                                        type="number"
                                        step="0.01"
                                        value={updates.price ?? variant.price ?? 0}
                                        onChange={(e) => setVariantUpdates(prev => ({
                                          ...prev,
                                          [variant.id]: { ...updates, price: parseFloat(e.target.value) || 0 }
                                        }))}
                                        className="h-7 text-xs"
                                      />
                                    ) : (
                                      <span
                                        className="cursor-pointer hover:bg-gray-100 px-1 py-0.5 rounded"
                                        onClick={() => setEditingVariants(prev => ({ ...prev, [variant.id]: true }))}
                                      >
                                        {formatCurrency(variant.price || 0)}
                                      </span>
                                    )}
                                  </TableCell>
                                  <TableCell className="text-xs">
                                    {variant.stock_quantity}
                                  </TableCell>
                                  <TableCell className="text-xs">
                                    {isEditing ? (
                                      <div className="flex gap-1">
                                        <Button
                                          type="button"
                                          size="sm"
                                          variant="outline"
                                          className="h-6 px-2 text-xs"
                                          onClick={() => handleVariantUpdate(variant.id)}
                                        >
                                          Save
                                        </Button>
                                        <Button
                                          type="button"
                                          size="sm"
                                          variant="ghost"
                                          className="h-6 px-2 text-xs"
                                          onClick={() => {
                                            setEditingVariants(prev => ({ ...prev, [variant.id]: false }))
                                            setVariantUpdates(prev => {
                                              const newUpdates = { ...prev }
                                              delete newUpdates[variant.id]
                                              return newUpdates
                                            })
                                          }}
                                        >
                                          Cancel
                                        </Button>
                                      </div>
                                    ) : (
                                      <Button
                                        type="button"
                                        size="sm"
                                        variant="ghost"
                                        className="h-6 px-2 text-xs"
                                        onClick={() => setEditingVariants(prev => ({ ...prev, [variant.id]: true }))}
                                      >
                                        Edit
                                      </Button>
                                    )}
                                  </TableCell>
                                </TableRow>
                                )
                              })}
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Pricing & Costs - Only show for simple products */}
          {!formData.has_variants && (
          <div className="border rounded-lg">
            <Button
              type="button"
              variant="ghost"
              onClick={() => toggleSection('pricing')}
              className="flex items-center justify-between w-full py-3.5 px-3 h-auto bg-gray-50 rounded-t-lg"
            >
              <div className="flex items-center space-x-2">
                <span className="font-medium">Pricing & Costs</span>
              </div>
              {expandedSections['pricing'] ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
            
            {expandedSections['pricing'] && (
              <div className="px-3 pt-3 pb-3 space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div className="space-y-1.5">
                    <Label htmlFor="base_cost" className="text-xs">Item Cost *</Label>
                    <Input
                      id="base_cost"
                      type="number"
                      step="0.01"
                      value={formData.base_cost !== null ? formData.base_cost : ''}
                      onChange={(e) => {
                        const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                        updateFormData('base_cost', value);
                      }}
                      placeholder="0.00"
                      required
                      className="h-8 text-xs placeholder:text-xs"
                    />
                  </div>
                  
                  <div className="space-y-1.5">
                    <Label htmlFor="packaging_cost" className="text-xs">Packaging Cost</Label>
                    <Input
                      id="packaging_cost"
                      type="number"
                      step="0.01"
                      value={formData.packaging_cost}
                      onChange={(e) => updateFormData('packaging_cost', parseFloat(e.target.value) || 0)}
                      placeholder="0.00"
                      className="h-8 text-xs placeholder:text-xs"
                    />
                  </div>
                  
                  <div className="space-y-1.5">
                    <Label htmlFor="total_cost" className="text-xs">Total Cost</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        value={formatCurrency(calculations.totalCost)}
                        disabled
                        className="bg-muted h-8 text-xs"
                      />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div className="space-y-1.5">
                    <Label htmlFor="price" className="text-xs">Selling Price *</Label>
                    <Input
                      id="price"
                      type="number"
                      step="0.01"
                      value={formData.price || ''}
                      onChange={(e) => updateFormData('price', parseFloat(e.target.value) || null)}
                      placeholder="0.00"
                      required={!formData.has_variants} // Required only for simple products
                      className="h-8 text-xs placeholder:text-xs"
                      disabled={formData.has_variants} // Disabled for variable products
                    />
                  </div>
                  
                  <div className="space-y-1.5">
                    <Label className="text-xs">Profit Margin</Label>
                    <div className={cn(
                      "px-3 py-0 flex items-center rounded-md border h-8", 
                      calculations.profitMargin < 0 ? "bg-red-50 border-red-200" : 
                      calculations.profitMargin < 15 ? "bg-yellow-50 border-yellow-200" : 
                      calculations.profitMargin < 30 ? "bg-green-50 border-green-200" : 
                      "bg-primary/10 border-primary/20"
                    )}>
                      <span className={cn("text-xs font-medium", calculations.marginColor)}>
                        {calculations.profitMargin.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                  
                  <div className="space-y-1.5">
                    <Label className="text-xs">Profit Amount</Label>
                    <div className={cn(
                      "px-3 py-0 flex items-center rounded-md border h-8", 
                      calculations.profitAmount < 0 ? "bg-red-50 border-red-200" : 
                      calculations.profitAmount < 5 ? "bg-yellow-50 border-yellow-200" : 
                      "bg-green-50 border-green-200"
                    )}>
                      <span className={cn("text-xs font-medium", calculations.marginColor)}>
                        {formatCurrency(calculations.profitAmount)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Sale Settings - Expandable */}
                <div className="border rounded-lg p-3">
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={() => setIsSaleExpanded(!isSaleExpanded)}
                    className="flex items-center justify-between w-full p-0 h-auto mb-2"
                  >
                    <span className="font-medium text-sm">Sale Settings</span>
                    {isSaleExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                  </Button>
                  
                  {isSaleExpanded && (
                    <div className="space-y-3">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div className="space-y-1.5">
                          <Label htmlFor="sale_price" className="text-xs">Sale Price</Label>
                          <Input
                            id="sale_price"
                            type="number"
                            step="0.01"
                            value={formData.sale_price || ''}
                            onChange={(e) => updateFormData('sale_price', parseFloat(e.target.value) || null)}
                            placeholder="0.00"
                            className="h-8 text-xs placeholder:text-xs"
                          />
                        </div>
                        
                        <div className="space-y-1.5">
                          <Label htmlFor="sale_start_date" className="text-xs">Sale Start Date</Label>
                          <DatePicker
                            date={formData.sale_start_date || undefined}
                            onDateChange={(date) => updateFormData('sale_start_date', date)}
                            className="h-8 text-xs"
                          />
                        </div>
                        
                        <div className="space-y-1.5">
                          <Label htmlFor="sale_end_date" className="text-xs">Sale End Date</Label>
                          <DatePicker
                            date={formData.sale_end_date || undefined}
                            onDateChange={(date) => updateFormData('sale_end_date', date)}
                            className="h-8 text-xs"
                          />
                        </div>
                      </div>
                      
                      {calculations.isOnSale && (
                        <div className="bg-orange-50 border border-orange-200 rounded-lg p-2">
                          <p className="text-xs text-orange-800">
                            <strong>Sale Active:</strong> Effective price is {formatCurrency(calculations.effectivePrice)} with {calculations.profitMargin.toFixed(1)}% margin
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          )}

          {/* Supplier & Sourcing */}
          <div className="border rounded-lg">
            <Button
              type="button"
              variant="ghost"
              onClick={() => toggleSection('supplier')}
              className="flex items-center justify-between w-full py-3.5 px-3 h-auto bg-gray-50 rounded-t-lg"
            >
              <div className="flex items-center space-x-2">
                <span className="font-medium">Supplier & Sourcing</span>
              </div>
              {expandedSections['supplier'] ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
            
            {expandedSections['supplier'] && (
              <div className="px-3 pt-3 pb-3 space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div className="space-y-1.5">
                    <Label htmlFor="supplier" className="text-xs">Supplier</Label>
                    <Input
                      id="supplier"
                      value={formData.supplier}
                      onChange={(e) => updateFormData('supplier', e.target.value)}
                      placeholder="Enter supplier name"
                      list="suppliers-list"
                      className="h-8 text-xs placeholder:text-xs"
                    />
                    <datalist id="suppliers-list">
                      {suppliers.map((supplier, index) => (
                        <option key={index} value={supplier} />
                      ))}
                    </datalist>
                  </div>
                  
                  <div className="space-y-1.5">
                    <Label htmlFor="purchase_date" className="text-xs">Purchase Date</Label>
                    <DatePicker
                      date={formData.purchase_date || undefined}
                      onDateChange={(date) => updateFormData('purchase_date', date)}
                      className="h-8 text-xs"
                    />
                  </div>
                  
                  <div className="space-y-1.5">
                    <Label htmlFor="batch_reference" className="text-xs">Batch Reference</Label>
                    <Input
                      id="batch_reference"
                      value={formData.batch_reference}
                      onChange={(e) => updateFormData('batch_reference', e.target.value)}
                      placeholder="Enter batch or lot number"
                      className="h-8 text-xs placeholder:text-xs"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Additional Information */}
          <div className="border rounded-lg">
            <Button
              type="button"
              variant="ghost"
              onClick={() => toggleSection('additional')}
              className="flex items-center justify-between w-full py-3.5 px-3 h-auto bg-gray-50 rounded-t-lg"
            >
              <div className="flex items-center space-x-2">
                <span className="font-medium">Additional Information</span>
              </div>
              {expandedSections['additional'] ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
            
            {expandedSections['additional'] && (
              <div className="px-3 pt-3 pb-3 space-y-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="space-y-1.5">
                    <Label htmlFor="brand" className="text-xs">Brand</Label>
                    <Input
                      id="brand"
                      value={formData.brand}
                      onChange={(e) => updateFormData('brand', e.target.value)}
                      placeholder="Enter product brand"
                      className="h-8 text-xs placeholder:text-xs"
                    />
                  </div>
                  
                  <div className="space-y-1.5">
                    <Label htmlFor="notes" className="text-xs">Notes</Label>
                    <Input
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => updateFormData('notes', e.target.value)}
                      placeholder="Enter additional notes"
                      className="h-8 text-xs placeholder:text-xs"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Form Actions */}
          <DialogFooter className="flex justify-end space-x-2 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
              className="h-8 text-xs"
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting} className="h-8 text-xs">
              {isSubmitting ? 'Updating...' : 'Update Product'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}