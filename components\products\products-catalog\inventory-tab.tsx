'use client'

import React, { useState, useEffect } from 'react'
import { 
  Package, 
  TrendingUp, 
  AlertTriangle, 
  XCircle 
} from 'lucide-react'
import { getSupabaseClient, type ProductRow, type ProductVariantRow } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { useToast } from '@/components/ui/use-toast'
import { EnhancedInventoryTable } from '../inventory/enhanced-inventory-table'

export function InventoryTab() {
  const [products, setProducts] = useState<(ProductRow & { variants?: ProductVariantRow[] })[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [debugInfo, setDebugInfo] = useState<any>(null)
  const { user } = useAuth()
  const { formatCurrency } = useCurrency()
  const { toast } = useToast()
  // Use getSupabaseClient instead of createSupabaseClient to ensure we get a fresh client
  const supabase = getSupabaseClient()

  // Load products on component mount
  useEffect(() => {
    if (user) {
      loadInventoryData()
    }
  }, [user])

  // Listen for refresh events from the adjust stock modal
  useEffect(() => {
    const handleRefresh = () => {
      loadInventoryData()
    }
    
    window.addEventListener('refreshInventoryData', handleRefresh)
    return () => {
      window.removeEventListener('refreshInventoryData', handleRefresh)
    }
  }, [])

  const loadInventoryData = async () => {
    if (!user?.id) return
    
    try {
      // First, log the query we're about to make
      console.log('Fetching inventory data with query:', `
        SELECT *,
        product_variants(*),
        categories(name)
        FROM products
        WHERE user_id = '${user.id}'
        AND is_active = true
      `)
      
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          categories(
            name
          ),
          product_variants(*)
        `)
        .eq('user_id', user.id)
        .eq('is_active', true)  // Only fetch active products for inventory

      if (error) {
        console.error('Error loading inventory data:', error)
        toast({
          title: "Error Loading Inventory",
          description: `Failed to load inventory data: ${error.message}`,
          variant: "destructive",
        })
        return
      }

      // Log the raw data for debugging
      console.log('Raw products data:', JSON.stringify(data, null, 2))
      
      // Debug: Examine the first product to see its structure
      if (data && data.length > 0) {
        console.log('First product structure:', {
          id: data[0].id,
          name: data[0].name,
          price: data[0].price,
          effective_price: data[0].effective_price,
          has_variants: data[0].has_variants,
          category_id: data[0].category_id,
          category_name: data[0].categories?.name,
          variants_count: data[0].product_variants?.length || 0,
          is_active: data[0].is_active
        })
        
        // Debug: If it has variants, examine the first variant
        if (data[0].has_variants && data[0].product_variants && data[0].product_variants.length > 0) {
          console.log('First variant structure:', {
            id: data[0].product_variants[0].id,
            price: data[0].product_variants[0].price,
            effective_price: data[0].product_variants[0].effective_price,
            stock_quantity: data[0].product_variants[0].stock_quantity,
            is_active: data[0].product_variants[0].is_active
          })
        }
      }
      
      // Store a processed version with renamed variant field for consistency
      const processedData = data?.map(product => ({
        ...product,
        variants: product.product_variants || [],
        category_name: product.categories?.name || null
      })) || []
      
      setProducts(processedData)
      
      // Save debug info
      setDebugInfo({
        productCount: processedData.length,
        productNames: processedData.map(p => p.name),
        hasVariantsCount: processedData.filter(p => p.has_variants).length,
        simpleProductsCount: processedData.filter(p => !p.has_variants).length,
        totalVariantsCount: processedData.reduce((total, p) => total + (p.variants?.length || 0), 0),
        categories: processedData.map(p => p.categories?.name).filter(Boolean)
      })
    } catch (error) {
      console.error('Error loading inventory data:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to load inventory data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Calculate inventory summary statistics
  const inventoryStats = React.useMemo(() => {
    // Initialize for detailed tracking of each product's contribution
    let totalValue = 0
    let totalProducts = products.length
    let inStockItems = 0
    let lowStockItems = 0
    let outOfStockItems = 0
    const productValues: { name: string; value: number; runningTotal: number }[] = []
    
    // Process each product
    products.forEach(product => {
      let productValue = 0
      
      // For variable products with variants
      if (product.has_variants && product.variants && product.variants.length > 0) {
        // Calculate stock status for each variant separately
        product.variants.forEach(variant => {
          const stockQty = variant.stock_quantity || 0
          const threshold = variant.low_stock_threshold || 10
          
          // Determine stock status for this variant
          if (stockQty === 0) {
            outOfStockItems++
          } else if (stockQty <= threshold) {
            lowStockItems++
          } else {
            inStockItems++
          }
          
          // Calculate variant value
          const currentPrice = variant.effective_price || variant.price || 0
          const variantValue = currentPrice * stockQty
          
          // Add to product value
          productValue += variantValue
        })
      } 
      // For simple products
      else {
        // Get the current selling price (use price if no special price is set)
        const currentPrice = product.effective_price || product.price || 0
        const stockQty = product.stock_quantity || 0
        productValue = currentPrice * stockQty
        
        // Determine stock status for simple products
        const threshold = product.low_stock_threshold || 10
        if (stockQty === 0) {
          outOfStockItems++
        } else if (stockQty <= threshold) {
          lowStockItems++
        } else {
          inStockItems++
        }
      }
      
      // Track this product's contribution
      totalValue += productValue
      productValues.push({
        name: product.name,
        value: productValue,
        runningTotal: totalValue
      })
    })
    
    return {
      totalValue,
      totalProducts,
      inStockItems,
      lowStockItems,
      outOfStockItems,
      productValues
    }
  }, [products])

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Inventory Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="rounded-full bg-blue-100 dark:bg-blue-900 p-2">
              <Package className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Products</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{inventoryStats.totalProducts}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="rounded-full bg-green-100 dark:bg-green-900 p-2">
              <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">In Stock</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{inventoryStats.inStockItems}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="rounded-full bg-yellow-100 dark:bg-yellow-900 p-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Low Stock</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{inventoryStats.lowStockItems}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="rounded-full bg-red-100 dark:bg-red-900 p-2">
              <XCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Out of Stock</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{inventoryStats.outOfStockItems}</p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Inventory Value */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Inventory Value</h3>
          <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {formatCurrency(inventoryStats.totalValue)}
          </p>
        </div>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Total value of all products in inventory
        </p>
      </div>
      
      {/* Enhanced Inventory Table */}
      <EnhancedInventoryTable 
        products={products} 
        onInventoryItemsChange={(items) => {
          // Optional: Handle inventory items change if needed
        }}
      />
    </div>
  )
}