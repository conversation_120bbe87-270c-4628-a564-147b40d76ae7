'use client'

import { useState, useEffect } from 'react'
import { 
  <PERSON><PERSON>, 
  Dialog<PERSON>ontent, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { getSupabaseClient, type ProductRow, type ProductVariantRow } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { X, Edit3 } from 'lucide-react'

interface ProductDetailViewProps {
  product: ProductRow | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onProductUpdated?: () => void
}

export function ProductDetailView({ 
  product, 
  open, 
  onOpenChange,
  onProductUpdated 
}: ProductDetailViewProps) {
  const [variants, setVariants] = useState<ProductVariantRow[]>([])
  const [loading, setLoading] = useState(false)
  const [updatingVariantId, setUpdatingVariantId] = useState<string | null>(null)
  const [variantStockUpdates, setVariantStockUpdates] = useState<Record<string, number>>({})
  const [variantPriceUpdates, setVariantPriceUpdates] = useState<Record<string, number>>({})
  const [editingPriceId, setEditingPriceId] = useState<string | null>(null)
  
  const { toast } = useToast()
  const { user } = useAuth()
  const { userCurrency, formatCurrency } = useCurrency()
  const supabase = getSupabaseClient()

  useEffect(() => {
    if (product && open && product.has_variants) {
      loadVariants()
    }
  }, [product, open])

  const loadVariants = async () => {
    if (!product?.id || !user?.id) return
    
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('product_variants')
        .select('*')
        .eq('product_id', product.id)
        .eq('user_id', user.id)
        .order('created_at')

      if (error) {
        console.error('Error loading variants:', error)
        toast({
          title: "Error Loading Variants",
          description: `Failed to load product variants: ${error.message}`,
          variant: "destructive",
        })
      } else {
        setVariants(data || [])
      }
    } catch (error) {
      console.error('Error loading variants:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to load product variants. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const updateVariantStock = async (variantId: string, newQuantity: number, reason: string) => {
    if (!user?.id || !product?.id) return
    
    setUpdatingVariantId(variantId)
    
    try {
      const variant = variants.find(v => v.id === variantId)
      if (!variant) {
        toast({
          title: "Error",
          description: "Variant not found",
          variant: "destructive",
        })
        return
      }

      const previousQuantity = variant.stock_quantity || 0;

      const { error: updateError } = await supabase
        .from('product_variants')
        .update({ stock_quantity: newQuantity })
        .eq('id', variantId)
        .eq('user_id', user.id)
      
      if (updateError) {
        toast({
          title: "Error Updating Stock",
          description: `Failed to update variant stock: ${updateError.message}`,
          variant: "destructive",
        })
        return
      }

      const { error: movementError } = await supabase
        .from('stock_movements')
        .insert([{
          user_id: user.id,
          product_id: product.id,
          variant_id: variantId,
          movement_type: newQuantity > previousQuantity ? 'stock_in' : 'stock_out',
          quantity: Math.abs(newQuantity - previousQuantity),
          reason,
          reference: `Manual adjustment - ${reason}`,
          created_at: new Date().toISOString()
        }])

      if (movementError) {
        console.error('Error creating stock movement:', movementError)
      }

      await loadVariants()
      onProductUpdated?.()
      window.dispatchEvent(new CustomEvent('productUpdated'))
      
      toast({
        title: "Stock Updated",
        description: `Variant stock quantity updated to ${newQuantity} units.`,
      })
    } catch (error) {
      console.error('Error updating variant stock:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to update variant stock. Please try again.",
        variant: "destructive",
      })
    } finally {
      setUpdatingVariantId(null)
      setVariantStockUpdates(prev => {
        const newUpdates = { ...prev }
        delete newUpdates[variantId]
        return newUpdates
      })
    }
  }

  const updateVariantPrice = async (variantId: string, newPrice: number) => {
    if (!user?.id || !product?.id) return
    
    setUpdatingVariantId(variantId)
    
    try {
      const { error: updateError } = await supabase
        .from('product_variants')
        .update({ price: newPrice })
        .eq('id', variantId)
        .eq('user_id', user.id)
      
      if (updateError) {
        toast({
          title: "Error Updating Price",
          description: `Failed to update variant price: ${updateError.message}`,
          variant: "destructive",
        })
        return
      }

      await loadVariants()
      onProductUpdated?.()
      window.dispatchEvent(new CustomEvent('productUpdated'))
      
      toast({
        title: "Price Updated",
        description: `Variant price updated to ${formatCurrency(newPrice, userCurrency)}.`,
      })
    } catch (error) {
      console.error('Error updating variant price:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to update variant price. Please try again.",
        variant: "destructive",
      })
    } finally {
      setUpdatingVariantId(null)
      setEditingPriceId(null)
      setVariantPriceUpdates(prev => {
        const newUpdates = { ...prev }
        delete newUpdates[variantId]
        return newUpdates
      })
    }
  }

  const handleVariantStockChange = (variantId: string, value: number) => {
    setVariantStockUpdates(prev => ({
      ...prev,
      [variantId]: value
    }))
  }

  const handleVariantPriceChange = (variantId: string, value: number) => {
    setVariantPriceUpdates(prev => ({
      ...prev,
      [variantId]: value
    }))
  }

  const handleVariantStockUpdate = (variantId: string, currentStock: number) => {
    const newQuantity = variantStockUpdates[variantId]
    if (newQuantity !== undefined && newQuantity >= 0) {
      updateVariantStock(variantId, newQuantity, "Manual stock adjustment from product detail view")
    }
  }

  const handleVariantPriceUpdate = (variantId: string, currentPrice: number) => {
    const newPrice = variantPriceUpdates[variantId]
    if (newPrice !== undefined && newPrice >= 0) {
      updateVariantPrice(variantId, newPrice)
    }
  }

  if (!product) return null

  const getStockStatus = (stock: number, threshold: number) => {
    if (stock === 0) return { status: 'Out of Stock', color: 'text-red-600', dotColor: 'bg-red-500' }
    if (stock <= threshold) return { status: 'Low Stock', color: 'text-yellow-600', dotColor: 'bg-yellow-500' }
    return { status: 'In Stock', color: 'text-green-600', dotColor: 'bg-green-500' }
  }

  const formatAttributes = (variant: ProductVariantRow) => {
    const attributes = []
    if (variant.size) attributes.push(`Size: ${variant.size}`)
    if (variant.color) attributes.push(`Color: ${variant.color}`)
    if (variant.material) attributes.push(`Material: ${variant.material}`)
    if (variant.style) attributes.push(`Style: ${variant.style}`)
    return attributes.join(', ') || 'No attributes'
  }

  const stockStatus = getStockStatus(product.stock_quantity || 0, product.low_stock_threshold || 10)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[85vh] overflow-y-auto p-0 text-xs">
        <div className="flex flex-col md:flex-row">
          {/* LEFT COLUMN: Financial Dashboard */}
          <div className="w-full md:w-1/2 p-8">
            {/* Product Header */}
            <header className="product-header">
              <DialogTitle className="text-xl font-bold mb-1">
                {product.name}
              </DialogTitle>
              <div className="text-sm text-gray-500">
                {product.base_sku || 'No SKU'}
              </div>
            </header>

            {/* Financial Information */}
            <section className="section mt-8">
              {product.has_variants ? (
                // For variable products, show a message that pricing is managed at variant level
                <div className="financial-card border border-gray-200 rounded-lg p-5 mb-6 bg-white shadow-sm">
                  <div className="label text-sm font-medium text-gray-500 mb-1">Variant Product</div>
                  <div className="value text-lg font-bold text-gray-900 mb-2">
                    Pricing managed per variant
                  </div>
                  <div className="text-sm text-gray-600">
                    This product has variants. Pricing information is managed individually for each variant in the table below.
                  </div>
                </div>
              ) : (
                // For simple products, show the pricing information
                <div className="financial-card border border-gray-200 rounded-lg p-5 mb-6 bg-white shadow-sm">
                  <div className="label text-sm font-medium text-gray-500 mb-1">Selling Price</div>
                  <div className="value text-3xl font-bold text-gray-900 mb-4">
                    {formatCurrency(product.effective_price || product.price || 0, userCurrency)}
                  </div>
                  <div className="cost-breakdown flex flex-wrap items-center gap-3 text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                    <span>{formatCurrency(product.base_cost || 0, userCurrency)} <span className="operator text-gray-400">(Base)</span></span>
                    <span className="operator text-gray-400">+</span>
                    <span>{formatCurrency(product.packaging_cost || 0, userCurrency)} <span className="operator text-gray-400">(Pkg)</span></span>
                    <span className="operator text-gray-400">=</span>
                    <span className="total-cost font-semibold">
                      {formatCurrency((product.base_cost || 0) + (product.packaging_cost || 0), userCurrency)} Total Cost
                    </span>
                  </div>
                </div>
              )}

              {!product.has_variants && (
                <div className="profit-card border border-gray-200 rounded-lg p-5 bg-white shadow-sm">
                  <div className="profit-header flex justify-between items-baseline mb-3">
                    <div className="label text-sm font-medium text-gray-500">Profit Margin</div>
                    <div className="value text-2xl font-bold text-green-500">
                      {product.profit_margin !== null ? `${product.profit_margin.toFixed(2)}%` : 'N/A'}
                    </div>
                  </div>
                  <div className="profit-meter h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div 
                      className="bar h-full bg-green-500 rounded-full" 
                      style={{ 
                        width: product.profit_margin !== null ? `${Math.min(product.profit_margin, 100)}%` : '0%' 
                      }}
                    ></div>
                  </div>
                </div>
              )}
            </section>
            
            {/* Product Description */}
            {product.description && (
              <div className="description-text mt-8 text-sm text-gray-600 leading-relaxed">
                {product.description}
              </div>
            )}
          </div>

          {/* RIGHT COLUMN: Details Panel */}
          <div className="w-full md:w-1/2 bg-gray-50 p-8 border-l border-gray-200">
            
            {/* Inventory Section */}
            <h3 className="detail-group-title text-xs font-semibold uppercase text-gray-500 tracking-wider mb-4">
              Inventory
            </h3>
            <div className="status-display flex items-center gap-2 mb-4">
              <span className={`dot w-2.5 h-2.5 rounded-full ${stockStatus.dotColor}`}></span>
              <span className={`font-semibold ${stockStatus.color}`}>{stockStatus.status}</span>
            </div>
            <dl className="details-list grid grid-cols-2 gap-4 text-sm">
              <dt className="text-gray-500">Current Stock</dt>
              <dd className="font-medium text-right">{product.stock_quantity || 0} units</dd>
              
              <dt className="text-gray-500">Low Stock Threshold</dt>
              <dd className="font-medium text-right">{product.low_stock_threshold || 10} units</dd>
            </dl>

            {/* Attributes Section */}
            <h3 className="detail-group-title text-xs font-semibold uppercase text-gray-500 tracking-wider mb-4 mt-8">
              Attributes
            </h3>
            <dl className="details-list grid grid-cols-2 gap-4 text-sm">
              <dt className="text-gray-500">Category</dt>
              <dd className="font-medium text-right">{(product as any).category_name || 'Uncategorized'}</dd>
              
              <dt className="text-gray-500">Brand</dt>
              <dd className="font-medium text-right">{product.brand || 'Not set'}</dd>
              
              <dt className="text-gray-500">Color</dt>
              <dd className="font-medium text-right">{product.color || 'Not specified'}</dd>
              
              <dt className="text-gray-500">Size</dt>
              <dd className="font-medium text-right">{product.size || 'Not specified'}</dd>
              
              <dt className="text-gray-500">Barcode</dt>
              <dd className="font-medium text-right font-mono">{product.barcode || 'Not set'}</dd>
            </dl>

            {/* Sourcing Section */}
            <h3 className="detail-group-title text-xs font-semibold uppercase text-gray-500 tracking-wider mb-4 mt-8">
              Sourcing
            </h3>
            <dl className="details-list grid grid-cols-2 gap-4 text-sm">
              <dt className="text-gray-500">Supplier</dt>
              <dd className="font-medium text-right">{product.supplier || 'Not set'}</dd>
              
              {product.purchase_date && (
                <>
                  <dt className="text-gray-500">Purchase Date</dt>
                  <dd className="font-medium text-right">
                    {new Date(product.purchase_date).toLocaleDateString()}
                  </dd>
                </>
              )}
              
              {product.batch_reference && (
                <>
                  <dt className="text-gray-500">Batch Reference</dt>
                  <dd className="font-medium text-right">{product.batch_reference}</dd>
                </>
              )}
            </dl>
          </div>
        </div>
        
        {/* Variants Section (if applicable) */}
        {product.has_variants && (
          <div className="border-t pt-6 mt-2 px-8 pb-6">
            <div className="border rounded-lg overflow-hidden">
              <div className="border-b border-gray-200 flex justify-between items-center">
                <h3 className="text-xs font-semibold text-black px-4 py-2">PRODUCT VARIANTS</h3>
                <div className="pr-4">
                  <Button 
                    variant="outline" 
                    size="sm"
                    className="h-8 text-xs px-3"
                    onClick={loadVariants}
                    disabled={loading}
                  >
                    {loading ? (
                      <span className="flex items-center">
                        <span className="h-3 w-3 mr-1 animate-spin border-2 border-current border-t-transparent rounded-full"></span>
                        Loading...
                      </span>
                    ) : (
                      "Refresh Variants"
                    )}
                  </Button>
                </div>
              </div>
              
              {loading ? (
                <div className="flex justify-center items-center h-32">
                  <div className="h-5 w-5 animate-spin border-2 border-current border-t-transparent rounded-full"></div>
                </div>
              ) : (
                <div>
                  <Table>
                    <TableHeader className="bg-gray-50">
                      <TableRow className="h-10">
                        <TableHead className="text-xs font-bold h-10">Variant</TableHead>
                        <TableHead className="text-xs font-bold h-10">SKU</TableHead>
                        <TableHead className="text-xs font-bold h-10 text-center">Attributes</TableHead>
                        <TableHead className="text-xs font-bold h-10 text-center">Stock</TableHead>
                        <TableHead className="text-xs font-bold h-10 text-right">Cost</TableHead>
                        <TableHead className="text-xs font-bold h-10 text-right">Price</TableHead>
                        <TableHead className="text-xs font-bold h-10 text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {variants.map((variant) => {
                        const stockStatus = getStockStatus(variant.stock_quantity, variant.low_stock_threshold || 10)
                        // For variants, use base_cost if available (new format), otherwise fall back to cost_adjustment (legacy)
                        const variantBaseCost = (variant.base_cost !== undefined && variant.base_cost !== null && variant.base_cost > 0)
                          ? variant.base_cost
                          : (variant.cost_adjustment || 0)
                        const variantPackagingCost = variant.packaging_cost || 0
                        const totalCost = variantBaseCost + variantPackagingCost
                        
                        return (
                          <TableRow key={variant.id} className="h-12">
                            <TableCell className="text-xs font-medium">
                              <div>{variant.variant_name || 'Unnamed Variant'}</div>
                              {variant.barcode && (
                                <div className="text-xs text-gray-500 font-mono mt-1">
                                  {variant.barcode}
                                </div>
                              )}
                            </TableCell>
                            <TableCell className="text-xs font-mono">{variant.sku}</TableCell>
                            <TableCell className="text-xs text-gray-500 text-center">
                              {formatAttributes(variant)}
                            </TableCell>
                            <TableCell className="text-xs text-center">
                              <Badge className={cn("text-xs", stockStatus.color)}>
                                {variant.stock_quantity} {stockStatus.status}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-xs text-right">
                              <div className="flex flex-col">
                                <span>{formatCurrency(variantBaseCost, userCurrency)}</span>
                                <span className="text-gray-500 text-xs">
                                  Base Cost Only
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="text-xs font-medium text-right">
                              {editingPriceId === variant.id ? (
                                <div className="flex items-center">
                                  <Input
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    value={variantPriceUpdates[variant.id] ?? variant.price}
                                    onChange={(e) => handleVariantPriceChange(variant.id, Number(e.target.value))}
                                    className="w-20 h-7 text-xs mr-1"
                                    onKeyDown={(e) => {
                                      if (e.key === 'Enter') {
                                        handleVariantPriceUpdate(variant.id, variant.price)
                                      } else if (e.key === 'Escape') {
                                        setEditingPriceId(null)
                                        setVariantPriceUpdates(prev => {
                                          const newUpdates = { ...prev }
                                          delete newUpdates[variant.id]
                                          return newUpdates
                                        })
                                      }
                                    }}
                                    autoFocus
                                  />
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    className="h-7 w-7 p-0"
                                    onClick={() => handleVariantPriceUpdate(variant.id, variant.price)}
                                    disabled={updatingVariantId === variant.id}
                                  >
                                    {updatingVariantId === variant.id ? (
                                      <span className="h-3 w-3 animate-spin border-2 border-current border-t-transparent rounded-full"></span>
                                    ) : (
                                      <span className="text-xs">✓</span>
                                    )}
                                  </Button>
                                </div>
                              ) : (
                                <div className="flex items-center justify-end">
                                  <span>{formatCurrency(variant.effective_price || variant.price || 0, userCurrency)}</span>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0 ml-1"
                                    onClick={() => {
                                      setEditingPriceId(variant.id)
                                      setVariantPriceUpdates(prev => ({
                                        ...prev,
                                        [variant.id]: variant.price
                                      }))
                                    }}
                                  >
                                    <Edit3 className="h-3 w-3" />
                                  </Button>
                                </div>
                              )}
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end space-x-2">
                                <div className="flex items-center border rounded">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-7 w-7 p-0"
                                    onClick={() => handleVariantStockChange(
                                      variant.id, 
                                      Math.max(0, (variantStockUpdates[variant.id] ?? variant.stock_quantity) - 1)
                                    )}
                                  >
                                    <span className="text-xs">-</span>
                                  </Button>
                                  <Input
                                    type="number"
                                    min="0"
                                    value={variantStockUpdates[variant.id] ?? variant.stock_quantity}
                                    onChange={(e) => handleVariantStockChange(variant.id, Number(e.target.value))}
                                    className="w-14 h-7 text-center border-0 p-0 text-xs"
                                  />
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-7 w-7 p-0"
                                    onClick={() => handleVariantStockChange(
                                      variant.id, 
                                      (variantStockUpdates[variant.id] ?? variant.stock_quantity) + 1
                                    )}
                                  >
                                    <span className="text-xs">+</span>
                                  </Button>
                                </div>
                                <Button
                                  size="sm"
                                  className="h-7 text-xs px-2"
                                  onClick={() => handleVariantStockUpdate(variant.id, variant.stock_quantity)}
                                  disabled={updatingVariantId === variant.id}
                                >
                                  {updatingVariantId === variant.id ? (
                                    <span className="flex items-center">
                                      <span className="h-3 w-3 mr-1 animate-spin border-2 border-current border-t-transparent rounded-full"></span>
                                      Updating
                                    </span>
                                  ) : (
                                    "Update"
                                  )}
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        )
                      })}
                    </TableBody>
                  </Table>
                  
                  {variants.length === 0 && (
                    <div className="text-center py-8 text-xs text-gray-500">
                      No variants found for this product
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}