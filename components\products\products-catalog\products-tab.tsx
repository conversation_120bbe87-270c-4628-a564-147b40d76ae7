'use client'

import React, { useState, useEffect, use<PERSON>allback, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { EditProductForm } from '@/components/products/products-catalog/edit-product-form'
import { ProductDetailView } from '@/components/products/products-catalog/product-detail-view'
import { ProductTableHeader } from '@/components/products/products-catalog/product-table-header'
import { type ProductFilters, INITIAL_PRODUCT_FILTERS } from '@/components/products/products-catalog/product-filters'
import { EnhancedProductTable } from '@/components/products/products-catalog/enhanced-product-table'
import { createSupabaseClient, type ProductRow, getAllProductsForUser } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { FileSpreadsheet } from 'lucide-react'

// Extended ProductRow type with category_name property (added during data transformation)
type ExtendedProductRow = ProductRow & {
  category_name?: string | null
}

interface ProductsTabProps {
  isMobile?: boolean
  className?: string
  onFiltersChange?: (filters: ProductFilters) => void
  onFilteredProductsChange?: (products: ExtendedProductRow[]) => void
  onSelectedProductsChange?: (productIds: string[]) => void
}

export function ProductsTab({ 
  isMobile = false, 
  className,
  onFiltersChange,
  onFilteredProductsChange: externalOnFilteredProductsChange,
  onSelectedProductsChange
}: ProductsTabProps) {
  console.log('ProductsTab: Component rendered');
  console.log('ProductsTab: User context:', useAuth());
  
  // Products state
  const [products, setProducts] = useState<ExtendedProductRow[]>([])
  const [isLoadingProducts, setIsLoadingProducts] = useState(false)
  const [loadError, setLoadError] = useState<string | null>(null)
  
  // Edit form state
  const [editingProduct, setEditingProduct] = useState<ProductRow | null>(null)
  const [isEditFormOpen, setIsEditFormOpen] = useState(false)
  
  // Detail view state
  const [viewingProduct, setViewingProduct] = useState<ProductRow | null>(null)
  const [isDetailViewOpen, setIsDetailViewOpen] = useState(false)
  
  // Filter state
  const [filters, setFilters] = useState<ProductFilters>(INITIAL_PRODUCT_FILTERS)
  const [filteredProducts, setFilteredProducts] = useState<ExtendedProductRow[]>([])
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  
  // Combined filter change handler
  const handleFiltersChange = (newFilters: ProductFilters) => {
    setFilters(newFilters)
    onFiltersChange?.(newFilters)
  }

  // Combined filtered products change handler (memoized to prevent infinite loops)
  const handleFilteredProductsChange = useCallback((products: ExtendedProductRow[]) => {
    setFilteredProducts(products)
    externalOnFilteredProductsChange?.(products)
  }, [externalOnFilteredProductsChange])

  // Handle selected products change
  const handleSelectedProductsChange = useCallback((productIds: string[]) => {
    setSelectedProducts(productIds)
    onSelectedProductsChange?.(productIds)
  }, [onSelectedProductsChange])
  
  const { toast } = useToast()
  const { user } = useAuth()
  console.log('ProductsTab: User from context:', user);
  const { userCurrency, formatCurrency } = useCurrency()
  const supabase = createSupabaseClient()

  // Extract unique categories from products
  const categories = useMemo(() => {
    const categorySet = new Set<{ value: string; label: string }>()
    
    products.forEach(product => {
      if (product.category_name) {
        categorySet.add({ 
          value: product.category_name, 
          label: product.category_name 
        })
      }
    })
    
    return Array.from(categorySet)
  }, [products])

  // Load products on component mount with better error handling
  useEffect(() => {
    console.log('ProductsTab: useEffect triggered');
    console.log('ProductsTab: User in useEffect:', user);
    console.log('ProductsTab: isLoadingProducts in useEffect:', isLoadingProducts);
    
    let isMounted = true;
    
    const loadProductsWithErrorHandling = async () => {
      console.log('ProductsTab: Attempting to load products for user:', user?.id);
      
      if (!user?.id) {
        console.log('ProductsTab: No user ID available');
        return;
      }
      
      // Skip loading if already loading
      if (isLoadingProducts) {
        console.log('ProductsTab: Already loading products, skipping');
        return;
      }
      
      setIsLoadingProducts(true);
      setLoadError(null);
      
      try {
        console.log('ProductsTab: Calling getAllProductsForUser');
        const data = await getAllProductsForUser(user.id);
        console.log('ProductsTab: Received data from getAllProductsForUser:', data?.length || 0, 'products');

        // Only update state if component is still mounted
        if (isMounted) {
          console.log('ProductsTab: Raw product data from Supabase:', data);
          
          // Transform data to include category name and process variants
          const transformedProducts = (data || []).map(product => {
            console.log('ProductsTab: Processing product:', product.id, product.name);
            console.log('ProductsTab: Product categories data:', product.categories);
            
            const transformed = {
              ...product,
              category_name: product.categories?.name || null,
              variants: product.product_variants || []
            };
            console.log('ProductsTab: Transformed product:', transformed);
            return transformed;
          });
          
          console.log('ProductsTab: All transformed products:', transformedProducts.length);
          setProducts(transformedProducts);
          
          // Also update filtered products with all products initially
          console.log('ProductsTab: Calling handleFilteredProductsChange with:', transformedProducts.length);
          handleFilteredProductsChange(transformedProducts);
        }
      } catch (error) {
        console.error('ProductsTab: Error loading products:', error);
        if (isMounted) {
          setLoadError(error instanceof Error ? error.message : 'Failed to load products');
          toast({
            title: "Unexpected Error",
            description: "Failed to load products. Please try again.",
            variant: "destructive",
          });
        }
      } finally {
        if (isMounted) {
          setIsLoadingProducts(false);
        }
      }
    };

    loadProductsWithErrorHandling();

    // Cleanup function to prevent state updates on unmounted components
    return () => {
      isMounted = false;
    };
  }, [user?.id, handleFilteredProductsChange]); // Fixed dependency array - removed isLoadingProducts and added handleFilteredProductsChange

  // Listen for product added/updated events to refresh the product list
  useEffect(() => {
    const handleProductAdded = () => {
      loadProducts()
    }
    
    const handleProductUpdated = () => {
      loadProducts()
    }

    window.addEventListener('productAdded', handleProductAdded)
    window.addEventListener('productUpdated', handleProductUpdated)
    
    return () => {
      window.removeEventListener('productAdded', handleProductAdded)
      window.removeEventListener('productUpdated', handleProductUpdated)
    }
  }, [])

  const loadProducts = async () => {
    if (!user?.id) return;
    
    setIsLoadingProducts(true);
    setLoadError(null);
    try {
      const data = await getAllProductsForUser(user.id);

      // Transform data to include category name and process variants
      const transformedProducts = (data || []).map(product => ({
        ...product,
        category_name: product.categories?.name || null,
        variants: product.product_variants || []
      }));
      setProducts(transformedProducts);
      
      // Update filtered products with all products initially
      handleFilteredProductsChange(transformedProducts);
    } catch (error) {
      console.error('Error loading products:', error);
      setLoadError(error instanceof Error ? error.message : 'Failed to load products');
      toast({
        title: "Unexpected Error",
        description: "Failed to load products. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingProducts(false);
    }
  }

  const handleProductAdded = () => {
    loadProducts()
  }

  const handleProductEdit = (product: ProductRow) => {
    setEditingProduct(product)
    setIsEditFormOpen(true)
  }

  const handleProductUpdated = () => {
    loadProducts()
    setIsEditFormOpen(false)
    setEditingProduct(null)
  }

  const handleProductDelete = async (productIds: string[]) => {
    if (!user?.id) return
    
    try {
      const { error } = await supabase
        .from('products')
        .delete()
        .in('id', productIds)
        .eq('user_id', user.id)
      
      if (error) {
        toast({
          title: "Error Deleting Products",
          description: `Failed to delete products: ${error.message}`,
          variant: "destructive",
        })
      } else {
        // Refresh the products list
        await loadProducts()
        
        // Dispatch dashboard refresh event
        window.dispatchEvent(new CustomEvent('dashboardRefresh'))
        
        toast({
          title: "Products Deleted",
          description: `Successfully deleted ${productIds.length} product(s).`,
        })
      }
    } catch (error) {
      console.error('Error deleting products:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to delete products. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleProductView = (product: ProductRow) => {
    setViewingProduct(product)
    setIsDetailViewOpen(true)
  }

  return (
    <div className={className}>
      {/* Edit Product Form - Modal */}
      {editingProduct && (
        <EditProductForm
          product={editingProduct}
          open={isEditFormOpen}
          onOpenChange={setIsEditFormOpen}
          onProductUpdated={handleProductUpdated}
        />
      )}

      {/* Product Detail View - Modal */}
      {viewingProduct && (
        <ProductDetailView
          product={viewingProduct}
          open={isDetailViewOpen}
          onOpenChange={setIsDetailViewOpen}
          onProductUpdated={handleProductUpdated}
        />
      )}

      {/* Products Table with Integrated Filters */}
      {isLoadingProducts && products.length === 0 ? (
        <div className="flex items-center justify-center py-8">
          <div className="text-muted-foreground">Loading products...</div>
        </div>
      ) : loadError && products.length === 0 ? (
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="text-red-500 mb-2">Error loading products</div>
            <Button onClick={loadProducts} variant="outline" size="sm">
              Retry
            </Button>
          </div>
        </div>
      ) : (
        <React.Fragment>
          {/* Integrated Table Header with Filters */}
          <ProductTableHeader
            products={products}
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onFilteredProductsChange={handleFilteredProductsChange}
            isMobile={isMobile}
            enableFilterUrlSync={true}
            categories={categories}
            className="mb-6"
          />
          
          {/* Enhanced Product Table */}
          <EnhancedProductTable
            products={filteredProducts}
            onProductEdit={handleProductEdit}
            onProductDelete={handleProductDelete}
            onProductView={handleProductView}
            onSelectedProductsChange={handleSelectedProductsChange}
            showBulkActions={true}
            defaultPageSize={isMobile ? 5 : 10}
            isMobile={isMobile}
          />
        </React.Fragment>
      )}
    </div>
  )
}