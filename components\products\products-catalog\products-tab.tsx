'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { EditProductForm } from '@/components/products/products-catalog/edit-product-form'
import { ProductDetailView } from '@/components/products/products-catalog/product-detail-view'
import { ProductTableHeader } from '@/components/products/products-catalog/product-table-header'
import { type ProductFilters, INITIAL_PRODUCT_FILTERS } from '@/components/products/products-catalog/product-filters'
import { EnhancedProductTable } from '@/components/products/products-catalog/enhanced-product-table'
import { getSupabaseClient, type ProductRow, getAllProductsForUser } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { FileSpreadsheet } from 'lucide-react'

// Extended ProductRow type with category_name property (added during data transformation)
type ExtendedProductRow = ProductRow & {
  category_name?: string | null
}

interface ProductsTabProps {
  isMobile?: boolean
  className?: string
  onFiltersChange?: (filters: ProductFilters) => void
  onFilteredProductsChange?: (products: ExtendedProductRow[]) => void
  onSelectedProductsChange?: (productIds: string[]) => void
}

export function ProductsTab({
  isMobile = false,
  className,
  onFiltersChange,
  onFilteredProductsChange: externalOnFilteredProductsChange,
  onSelectedProductsChange
}: ProductsTabProps) {
  // Products state
  const [products, setProducts] = useState<ExtendedProductRow[]>([])
  const [isLoadingProducts, setIsLoadingProducts] = useState(false)
  const [loadingTimeout, setLoadingTimeout] = useState<NodeJS.Timeout | null>(null)
  const [retryCount, setRetryCount] = useState(0)

  // Debug logging helper
  const logDebug = (message: string, data?: any) => {
    const timestamp = new Date().toISOString()
    console.log(`[${timestamp}] ProductsTab: ${message}`, data || '')
  }
  
  // Edit form state
  const [editingProduct, setEditingProduct] = useState<ProductRow | null>(null)
  const [isEditFormOpen, setIsEditFormOpen] = useState(false)
  
  // Detail view state
  const [viewingProduct, setViewingProduct] = useState<ProductRow | null>(null)
  const [isDetailViewOpen, setIsDetailViewOpen] = useState(false)
  
  // Filter state
  const [filters, setFilters] = useState<ProductFilters>(INITIAL_PRODUCT_FILTERS)
  const [filteredProducts, setFilteredProducts] = useState<ExtendedProductRow[]>([])
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  
  // Combined filter change handler
  const handleFiltersChange = (newFilters: ProductFilters) => {
    setFilters(newFilters)
    onFiltersChange?.(newFilters)
  }

  // Combined filtered products change handler (memoized to prevent infinite loops)
  const handleFilteredProductsChange = useCallback((products: ExtendedProductRow[]) => {
    setFilteredProducts(products)
    externalOnFilteredProductsChange?.(products)
  }, [externalOnFilteredProductsChange])

  // Handle selected products change
  const handleSelectedProductsChange = useCallback((productIds: string[]) => {
    setSelectedProducts(productIds)
    onSelectedProductsChange?.(productIds)
  }, [onSelectedProductsChange])
  
  const { toast } = useToast()
  const { user } = useAuth()
  const { userCurrency, formatCurrency } = useCurrency()
  const supabase = getSupabaseClient()

  // Extract unique categories from products
  const categories = useMemo(() => {
    const categorySet = new Set<{ value: string; label: string }>()
    
    products.forEach(product => {
      if (product.category_name) {
        categorySet.add({ 
          value: product.category_name, 
          label: product.category_name 
        })
      }
    })
    
    return Array.from(categorySet)
  }, [products])

  // Load products on component mount
  useEffect(() => {
    if (user) {
      logDebug('Component mounted, loading products', { userId: user.id })
      loadProducts()
    }
  }, [user])

  // Listen for product added/updated events to refresh the product list
  useEffect(() => {
    const handleProductAdded = () => {
      loadProducts()
    }

    const handleProductUpdated = () => {
      loadProducts()
    }

    window.addEventListener('productAdded', handleProductAdded)
    window.addEventListener('productUpdated', handleProductUpdated)

    return () => {
      window.removeEventListener('productAdded', handleProductAdded)
      window.removeEventListener('productUpdated', handleProductUpdated)
    }
  }, [])

  // Handle tab visibility changes to refresh data when returning to the tab
  useEffect(() => {
    const handleVisibilityChange = () => {
      logDebug('Visibility state changed', {
        visibilityState: document.visibilityState,
        hasUser: !!user,
        isLoading: isLoadingProducts
      })

      if (document.visibilityState === 'visible' && user) {
        logDebug('Tab became visible, checking if refresh needed')

        // If we're currently loading, don't trigger another load
        if (!isLoadingProducts) {
          logDebug('Refreshing products data on tab visibility')
          loadProducts()
        } else {
          logDebug('Already loading, skipping refresh')
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [user, isLoadingProducts])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (loadingTimeout) {
        clearTimeout(loadingTimeout)
      }
    }
  }, [loadingTimeout])

  // Handle network connectivity changes
  useEffect(() => {
    const handleOnline = () => {
      logDebug('Network connection restored, refreshing data')
      if (user && !isLoadingProducts) {
        loadProducts()
      }
    }

    const handleOffline = () => {
      logDebug('Network connection lost')
      // Clear any pending timeouts when going offline
      if (loadingTimeout) {
        clearTimeout(loadingTimeout)
        setLoadingTimeout(null)
      }
      setIsLoadingProducts(false)
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [user, isLoadingProducts, loadingTimeout])

  const loadProducts = async (isRetry = false) => {
    if (!user?.id) return

    // Clear any existing timeout
    if (loadingTimeout) {
      clearTimeout(loadingTimeout)
      setLoadingTimeout(null)
    }

    setIsLoadingProducts(true)

    // Set a timeout to detect stuck loading state
    const timeout = setTimeout(() => {
      logDebug('Loading timeout reached, attempting recovery', { retryCount })
      setIsLoadingProducts(false)

      if (retryCount < 3) {
        logDebug(`Retrying load (attempt ${retryCount + 1}/3)`)
        setRetryCount(prev => prev + 1)
        setTimeout(() => loadProducts(true), 1000 * (retryCount + 1)) // Exponential backoff
      } else {
        logDebug('Max retries reached, giving up')
        toast({
          title: "Loading Timeout",
          description: "Products are taking too long to load. Please refresh the page or try again.",
          variant: "destructive",
        })
        setRetryCount(0)
      }
    }, 10000) // 10 second timeout

    setLoadingTimeout(timeout)

    try {
      logDebug(`Loading products${isRetry ? ' (retry)' : ''}`, { userId: user.id, retryCount })
      const data = await getAllProductsForUser(user.id)

      // Transform data to include category name and process variants
      const transformedProducts = (data || []).map(product => ({
        ...product,
        category_name: product.categories?.name || null,
        variants: product.product_variants || []
      }))
      setProducts(transformedProducts)
      setRetryCount(0) // Reset retry count on success
      logDebug(`Successfully loaded ${transformedProducts.length} products`)
    } catch (error) {
      logDebug('Error loading products', { error: error.message, isRetry, retryCount })

      // Only show toast for non-retry attempts to avoid spam
      if (!isRetry) {
        toast({
          title: "Unexpected Error",
          description: "Failed to load products. Please try again.",
          variant: "destructive",
        })
      }

      // Attempt retry if this wasn't already a retry
      if (!isRetry && retryCount < 3) {
        logDebug(`Retrying after error (attempt ${retryCount + 1}/3)`)
        setRetryCount(prev => prev + 1)
        setTimeout(() => loadProducts(true), 2000 * (retryCount + 1)) // Exponential backoff
      }
    } finally {
      // Clear timeout and loading state
      if (loadingTimeout) {
        clearTimeout(loadingTimeout)
        setLoadingTimeout(null)
      }
      setIsLoadingProducts(false)
    }
  }

  const handleProductAdded = () => {
    loadProducts()
  }

  const handleProductEdit = (product: ProductRow) => {
    setEditingProduct(product)
    setIsEditFormOpen(true)
  }

  const handleProductUpdated = () => {
    loadProducts()
    setIsEditFormOpen(false)
    setEditingProduct(null)
  }

  const handleProductDelete = async (productIds: string[]) => {
    if (!user?.id) return
    
    try {
      const { error } = await supabase
        .from('products')
        .delete()
        .in('id', productIds)
        .eq('user_id', user.id)
      
      if (error) {
        toast({
          title: "Error Deleting Products",
          description: `Failed to delete products: ${error.message}`,
          variant: "destructive",
        })
      } else {
        // Refresh the products list
        await loadProducts()
        
        // Dispatch dashboard refresh event
        window.dispatchEvent(new CustomEvent('dashboardRefresh'))
        
        toast({
          title: "Products Deleted",
          description: `Successfully deleted ${productIds.length} product(s).`,
        })
      }
    } catch (error) {
      console.error('Error deleting products:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to delete products. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleProductView = (product: ProductRow) => {
    setViewingProduct(product)
    setIsDetailViewOpen(true)
  }

  return (
    <div className={className}>
      {/* Edit Product Form - Modal */}
      {editingProduct && (
        <EditProductForm
          product={editingProduct}
          open={isEditFormOpen}
          onOpenChange={setIsEditFormOpen}
          onProductUpdated={handleProductUpdated}
        />
      )}

      {/* Product Detail View - Modal */}
      {viewingProduct && (
        <ProductDetailView
          product={viewingProduct}
          open={isDetailViewOpen}
          onOpenChange={setIsDetailViewOpen}
          onProductUpdated={handleProductUpdated}
        />
      )}

      {/* Products Table with Integrated Filters */}
      {isLoadingProducts ? (
        <div className="flex flex-col items-center justify-center py-8 space-y-4">
          <div className="text-muted-foreground">Loading products...</div>
          {retryCount > 0 && (
            <div className="text-sm text-muted-foreground">
              Retry attempt {retryCount}/3
            </div>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              logDebug('Manual retry triggered by user')
              setIsLoadingProducts(false)
              setTimeout(() => loadProducts(), 100)
            }}
            className="mt-2"
          >
            Retry Now
          </Button>
        </div>
      ) : (
        <>
          {/* Integrated Table Header with Filters */}
          <ProductTableHeader
            products={products}
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onFilteredProductsChange={handleFilteredProductsChange}
            isMobile={isMobile}
            enableFilterUrlSync={true}
            categories={categories}
            className="mb-6"
          />
          
          {/* Enhanced Product Table */}
          <EnhancedProductTable
            products={filteredProducts}
            onProductEdit={handleProductEdit}
            onProductDelete={handleProductDelete}
            onProductView={handleProductView}
            onSelectedProductsChange={handleSelectedProductsChange}
            showBulkActions={true}
            defaultPageSize={isMobile ? 5 : 10}
            isMobile={isMobile}
          />
        </>
      )}
    </div>
  )
}
