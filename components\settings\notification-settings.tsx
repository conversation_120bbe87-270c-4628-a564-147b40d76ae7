'use client'

import { useState, useEffect } from 'react'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { createSupabaseClient } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useToast } from '@/components/ui/use-toast'

export function NotificationSettings() {
  const { user } = useAuth()
  const { toast } = useToast()
  const supabase = createSupabaseClient()
  
  const [mounted, setMounted] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  
  // Notification preferences state
  const [emailNotifications, setEmailNotifications] = useState(true)
  const [pushNotifications, setPushNotifications] = useState(true)
  
  // Track unsaved changes
  const [originalSettings, setOriginalSettings] = useState({
    email_notifications: true,
    push_notifications: true
  })

  useEffect(() => {
    setMounted(true)
    loadNotificationPreferences()
  }, [user])

  const loadNotificationPreferences = async () => {
    if (!user) return
    
    try {
      setIsLoading(true)
      const { data, error } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', user.id)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 means no rows returned
        console.error('Error loading notification preferences:', error)
        toast({
          title: "Error",
          description: "Failed to load notification preferences. Using default settings.",
          variant: "destructive",
        })
        return
      }

      if (data) {
        setEmailNotifications(data.email_notifications ?? true)
        setPushNotifications(data.push_notifications ?? true)
        
        setOriginalSettings({
          email_notifications: data.email_notifications ?? true,
          push_notifications: data.push_notifications ?? true
        })
      } else {
        // No preferences found, initialize with defaults
        setEmailNotifications(true)
        setPushNotifications(true)
        
        setOriginalSettings({
          email_notifications: true,
          push_notifications: true
        })
      }
    } catch (error) {
      console.error('Error loading notification preferences:', error)
      toast({
        title: "Error",
        description: "Failed to load notification preferences. Using default settings.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const hasUnsavedChanges = () => {
    return (
      emailNotifications !== originalSettings.email_notifications ||
      pushNotifications !== originalSettings.push_notifications
    )
  }

  const saveNotificationPreferences = async () => {
    if (!user) return
    
    try {
      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          email_notifications: emailNotifications,
          push_notifications: pushNotifications,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        })

      if (error) {
        throw error
      }

      // Update original settings to reflect saved state
      setOriginalSettings({
        email_notifications: emailNotifications,
        push_notifications: pushNotifications
      })

      toast({
        title: "Preferences Saved",
        description: "Your notification preferences have been saved successfully.",
      })
    } catch (error) {
      console.error('Error saving notification preferences:', error)
      toast({
        title: "Error",
        description: "Failed to save notification preferences. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Notify parent component of changes
  useEffect(() => {
    if (mounted && !isLoading) {
      window.dispatchEvent(new CustomEvent('notificationPreferencesChanged'))
    }
  }, [emailNotifications, pushNotifications, mounted, isLoading])

  // Listen for save event
  useEffect(() => {
    const handleSaveEvent = () => {
      saveNotificationPreferences()
    }

    window.addEventListener('saveNotificationPreferences', handleSaveEvent)
    
    return () => {
      window.removeEventListener('saveNotificationPreferences', handleSaveEvent)
    }
  }, [emailNotifications, pushNotifications])

  if (!mounted) {
    return (
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-4 py-3 border-b border-gray-200">
          <h3 className="text-base font-medium text-gray-900">Notification Settings</h3>
        </div>
        <div className="p-4 space-y-4">
          {[...Array(2)].map((_, i) => (
            <div key={i} className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-3 w-48 bg-gray-200 rounded animate-pulse"></div>
              </div>
              <div className="h-5 w-10 bg-gray-200 rounded-full animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="px-4 py-3 border-b border-gray-200">
        <h3 className="text-base font-medium text-gray-900">Notification Settings</h3>
      </div>
      <div className="p-4 space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center py-4">
            <div className="text-sm text-gray-500">Loading notification preferences...</div>
          </div>
        ) : (
          <>
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="email-notifications" className="text-sm font-medium text-gray-700">
                  Email Notifications
                </Label>
                <p className="text-xs text-gray-500">
                  Receive email notifications for important updates
                </p>
              </div>
              <Switch
                id="email-notifications"
                checked={emailNotifications}
                onCheckedChange={setEmailNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="push-notifications" className="text-sm font-medium text-gray-700">
                  Push Notifications
                </Label>
                <p className="text-xs text-gray-500">
                  Receive push notifications in your browser
                </p>
              </div>
              <Switch
                id="push-notifications"
                checked={pushNotifications}
                onCheckedChange={setPushNotifications}
              />
            </div>
          </>
        )}
      </div>
    </div>
  )
}