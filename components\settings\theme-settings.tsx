'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'

export function ThemeSettings({ 
  onThemeChange,
  currentTheme
}: { 
  onThemeChange: (theme: 'light' | 'dark') => void,
  currentTheme: 'light' | 'dark'
}) {
  const [mounted, setMounted] = useState(false)

  // Ensure component is mounted before rendering to avoid hydration issues
  useEffect(() => {
    setMounted(true)
  }, [])

  const handleThemeChange = (newTheme: 'light' | 'dark') => {
    onThemeChange(newTheme)
  }

  if (!mounted) {
    return (
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-4 py-3 border-b border-gray-200">
          <h3 className="text-base font-medium text-gray-900">Theme Settings</h3>
        </div>
        <div className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Theme</h3>
              <p className="text-xs text-gray-500">Select your preferred theme</p>
            </div>
            <div className="w-24 h-8 bg-gray-200 rounded-md animate-pulse"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="px-4 py-3 border-b border-gray-200">
        <h3 className="text-base font-medium text-gray-900">Theme Settings</h3>
      </div>
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium text-gray-900">Theme</h3>
            <p className="text-xs text-gray-500">Select your preferred theme</p>
          </div>
          <div className="flex space-x-2">
            <Button
              variant={currentTheme === 'light' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleThemeChange('light')}
            >
              Light
            </Button>
            <Button
              variant={currentTheme === 'dark' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleThemeChange('dark')}
            >
              Dark
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}