'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  LayoutDashboard, 
  Package, 
  Receipt, 
  BarChart3, 
  Users,
  Settings,
  Menu,
  X,
  Palette,
  ChevronDown,
  ChevronRight,
  Bell
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { 
    name: 'Products', 
    href: '/dashboard/products', 
    icon: Package,
    children: [
      { name: 'Products Catalog', href: '/dashboard/products' },
      { name: 'Inventory', href: '/dashboard/products/inventory' },
      { name: 'Purchase Orders', href: '/dashboard/products/purchase-orders' },
      { name: 'Analytics', href: '/dashboard/products/analytics' },
      { name: 'Settings', href: '/dashboard/products/settings' },
    ]
  },
  { name: 'Expenses', href: '/dashboard/expenses', icon: Receipt },
  { name: 'Reporting', href: '/dashboard/reports', icon: BarChart3 },
  { name: 'Customers', href: '/dashboard/customers', icon: Users },
  { name: 'Design', href: '/dashboard/design', icon: Palette },
  { name: 'Settings', href: '/dashboard/settings', icon: Settings },
]

export function Sidebar() {
  const [isOpen, setIsOpen] = useState(false)
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({
    products: false,
  })
  const pathname = usePathname()

  // Automatically manage expanded state based on current route
  useEffect(() => {
    const newExpandedMenus = { ...expandedMenus }
    
    // Check if we're on any products sub-page
    if (pathname.startsWith('/dashboard/products')) {
      newExpandedMenus.products = true
    } else {
      // Collapse products menu when navigating away
      newExpandedMenus.products = false
    }
    
    setExpandedMenus(newExpandedMenus)
  }, [pathname])

  const toggleMenu = (menuName: string) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuName]: !prev[menuName]
    }))
  }

  return (
    <>
      {/* Mobile menu button */}
      <Button
        variant="ghost"
        size="icon"
        className="md:hidden fixed top-4 left-4 z-50"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
      </Button>

      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 w-56 bg-white text-gray-800 shadow-md transform transition-transform duration-300 ease-in-out md:translate-x-0 md:static md:inset-0 md:z-auto border-r border-gray-300",
        isOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center h-14 px-4 border-b border-gray-200">
            <h1 className="text-xl font-bold text-gray-900">ONKO</h1>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 pt-3 pb-4">
            <div className="space-y-1">
              {navigation.map((item) => {
                const Icon = item.icon
                const isActive = pathname === item.href || 
                  (item.href !== '/dashboard' && pathname.startsWith(item.href))
                
                // Check if this is the Products item with children
                if (item.children) {
                  const isExpanded = expandedMenus.products
                  const hasActiveChild = item.children.some(child => {
                    return pathname === child.href || pathname.startsWith(child.href)
                  })
                  
                  return (
                    <div key={item.name}>
                      <button
                        onClick={() => toggleMenu('products')}
                        className={cn(
                          "flex items-center justify-between w-full px-3 py-2 text-xs rounded-lg transition-colors",
                          isActive || hasActiveChild
                            ? "bg-sidebar-active text-white"
                            : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                        )}
                      >
                        <div className="flex items-center">
                          <Icon className="mr-2 h-4 w-4" />
                          <span>{item.name}</span>
                        </div>
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </button>
                      
                      {isExpanded && (
                        <div className="ml-4 mt-1 space-y-1 border-l border-gray-200 pl-4">
                          {item.children.map((child) => {
                            const isChildActive = pathname === child.href
                            
                            return (
                              <Link
                                key={child.name}
                                href={child.href}
                                className={cn(
                                  "flex items-center px-3 py-2 text-xs rounded-lg transition-colors",
                                  isChildActive
                                    ? "bg-sidebar-submenu-active text-sidebar-submenu-text"
                                    : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                                )}
                                onClick={() => setIsOpen(false)}
                              >
                                <span>{child.name}</span>
                              </Link>
                            )
                          })}
                        </div>
                      )}
                    </div>
                  )
                }
                
                // Regular menu item (no children)
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                      "flex items-center px-3 py-2 text-xs rounded-lg transition-colors",
                      isActive
                        ? "bg-sidebar-active text-white"
                        : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                    )}
                    onClick={() => setIsOpen(false)}
                  >
                    <Icon className="mr-2 h-4 w-4" />
                    <span>{item.name}</span>
                  </Link>
                )
              })}
            </div>
          </nav>
        </div>
      </div>

      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  )
}