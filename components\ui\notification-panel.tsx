'use client'

import { useState, useEffect, useRef } from 'react'
import { Bell, X, CheckCircle, AlertTriangle, Info } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { formatDistanceToNow } from 'date-fns'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { NotificationService } from '@/lib/notification-service'
import { centralizedNotificationService } from '@/lib/centralized-notification-service'
import Link from 'next/link'
import { toast } from '@/components/ui/use-toast'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
}

export function NotificationPanel() {
  const [isOpen, setIsOpen] = useState(false)
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const supabase = createClientComponentClient()
  const notificationService = new NotificationService()
  const unsubscribeRef = useRef<(() => void) | null>(null)
  const unsubscribeCustomEventRef = useRef<(() => void) | null>(null)

  // Load notifications and count
  useEffect(() => {
    loadNotifications()
    loadUnreadCount()

    // Set up subscription using centralized service
    unsubscribeRef.current = centralizedNotificationService.subscribe(
      (newNotification: any) => {
        // Add new notification to the top of the list only if it doesn't already exist
        setNotifications(prev => {
          // Check if notification already exists
          const exists = prev.some(notification => notification.id === newNotification.id)
          if (!exists) {
            return [newNotification, ...prev]
          }
          return prev
        })
        // Update unread count for new notifications
        if (!newNotification.is_read) {
          setUnreadCount(prev => prev + 1)
        }
      },
      (updatedNotification: any) => {
        // Update notification in the list
        setNotifications(prev => 
          prev.map(notification => 
            notification.id === updatedNotification.id 
              ? updatedNotification 
              : notification
          )
        )
        
        // Update unread count if this notification was marked as read
        if (updatedNotification.is_read) {
          setUnreadCount(prev => Math.max(0, prev - 1))
        }
      },
      (deletedNotification: any) => {
        // Remove deleted notification from the list
        setNotifications(prev => 
          prev.filter(notification => notification.id !== deletedNotification.id)
        )
        
        // Update unread count if this notification was unread
        if (!deletedNotification.is_read) {
          setUnreadCount(prev => Math.max(0, prev - 1))
        }
      }
    )

    // Subscribe to custom events for "mark all as read"
    unsubscribeCustomEventRef.current = centralizedNotificationService.subscribeToEvent(
      'notifications-marked-as-read',
      () => {
        // Update all notifications to read
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, is_read: true }))
        )
        setUnreadCount(0)
      }
    )

    // Set up periodic refresh of unread count (every 30 seconds)
    const refreshInterval = setInterval(() => {
      // Only log in development mode to reduce console noise
      if (process.env.NODE_ENV === 'development') {
        console.log('NotificationPanel: Refreshing unread count')
      }
      loadUnreadCount()
    }, 30000)

    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
        unsubscribeRef.current = null
      }
      if (unsubscribeCustomEventRef.current) {
        unsubscribeCustomEventRef.current()
        unsubscribeCustomEventRef.current = null
      }
      clearInterval(refreshInterval)
    }
  }, [])

  const loadNotifications = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        const notifications = await notificationService.getNotifications(user.id, 5)
        setNotifications(notifications)
      }
    } catch (error) {
      console.error('Error loading notifications:', error)
    }
  }

  const loadUnreadCount = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        const count = await notificationService.getUnreadNotificationCount(user.id)
        setUnreadCount(count)
      }
    } catch (error) {
      console.error('Error loading unread count:', error)
    }
  }

  const markAsRead = async (id: string) => {
    try {
      await notificationService.markAsRead(id)
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: true } 
            : notification
        )
      )
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const markAllAsRead = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        // Update database
        await notificationService.markAllAsRead(user.id)
        
        // Update local state immediately for instant feedback
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, is_read: true }))
        )
        setUnreadCount(0)
        
        // Broadcast the change to other components
        centralizedNotificationService.broadcastEvent('notifications-marked-as-read', { userId: user.id })
        
        // Show confirmation toast
        toast({
          title: "All notifications marked as read",
          description: "All your notifications have been marked as read."
        })
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      toast({
        title: "Error",
        description: "Failed to mark all notifications as read. Please try again.",
        variant: "destructive"
      })
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'product_deleted':
        return <X className="h-4 w-4 text-red-500" />
      case 'info':
      default:
        return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'border-l-green-500'
      case 'warning':
        return 'border-l-yellow-500'
      case 'product_deleted':
        return 'border-l-red-500'
      case 'info':
      default:
        return 'border-l-blue-500'
    }
  }

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size="sm"
        className="rounded-full relative hover:bg-gray-100 focus:ring-0"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Notifications"
      >
        <Bell className="h-4 w-4 text-gray-600" />
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-red-500" />
        )}
      </Button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-semibold text-gray-900">Notifications</h3>
              {unreadCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs text-blue-600 hover:text-blue-800"
                  onClick={markAllAsRead}
                >
                  Mark all as read
                </Button>
              )}
            </div>
          </div>

          <div className="max-h-80 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="p-4 text-center text-sm text-gray-500">
                No notifications
              </div>
            ) : (
              <ul className="divide-y divide-gray-200">
                {notifications.map((notification) => (
                  <li 
                    key={notification.id}
                    className={`p-4 hover:bg-gray-50 ${!notification.is_read ? 'bg-blue-50' : ''} ${getNotificationColor(notification.type)}`}
                  >
                    <div className="flex items-start">
                      <div className="flex-shrink-0 pt-0.5">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="ml-3 flex-1">
                        <p className="text-sm font-medium text-gray-900">
                          {notification.title}
                        </p>
                        <p className="mt-1 text-sm text-gray-500">
                          {notification.message}
                        </p>
                        <p className="mt-1 text-xs text-gray-400">
                          {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                        </p>
                      </div>
                      <div className="ml-4 flex flex-shrink-0">
                        {!notification.is_read && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-5 w-5 p-0 text-gray-400 hover:text-gray-600"
                            onClick={() => markAsRead(notification.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>

          <div className="border-t border-gray-200 p-2">
            <Link 
              href="/dashboard/notifications" 
              className="block"
              onClick={() => setIsOpen(false)} // Close the dropdown when navigating
            >
              <Button 
                variant="ghost" 
                size="sm" 
                className="w-full text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50"
              >
                View all notifications
              </Button>
            </Link>
          </div>
        </div>
      )}
    </div>
  )
}