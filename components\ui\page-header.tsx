'use client'

import { cn } from '@/lib/utils'

interface PageHeaderProps {
  title: string
  description?: string
  className?: string
  children?: React.ReactNode
}

export function PageHeader({ 
  title, 
  description, 
  className,
  children 
}: PageHeaderProps) {
  return (
    <div className={cn("mb-4", className)}>
      <div className="bg-white shadow-sm rounded-md px-4 py-3 border-l-4 border-primary">
        <h1 className="text-xl font-medium text-gray-900">{title}</h1>
      </div>
      {children}
    </div>
  )
}