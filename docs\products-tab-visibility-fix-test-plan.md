# Products Tab Visibility Fix - Test Plan

## Overview
This document outlines the test plan for fixing the issue where the products table gets stuck on "Loading products..." when switching browser tabs and returning.

## Implemented Fixes

### 1. Tab Visibility Handling
- Added `document.visibilitychange` event listener
- Refreshes products data when tab becomes visible
- Prevents duplicate loads when already loading

### 2. Loading State Recovery Logic
- 10-second timeout for stuck loading states
- Automatic retry with exponential backoff (max 3 attempts)
- Manual retry button in loading state

### 3. Network Connectivity Monitoring
- Handles `online`/`offline` events
- Refreshes data when connection is restored
- Clears loading state when going offline

### 4. Error Boundary
- Catches and handles component errors
- Provides retry mechanism
- Shows detailed error info in development

### 5. Debug Logging
- Comprehensive logging for all events
- Tracks visibility changes, network events, and loading states
- Helps with future debugging

## Test Scenarios

### Scenario 1: Basic Tab Switching
**Steps:**
1. Navigate to products page
2. Wait for products to load completely
3. Switch to another browser tab
4. Wait 5-10 seconds
5. Return to products tab

**Expected Result:**
- Products should refresh automatically
- No stuck loading state
- Debug logs should show visibility change events

### Scenario 2: Tab Switch During Loading
**Steps:**
1. Navigate to products page
2. Immediately switch to another tab while products are loading
3. Wait 5-10 seconds
4. Return to products tab

**Expected Result:**
- Loading should complete or restart
- No permanent stuck state
- Retry mechanism should work if needed

### Scenario 3: Long Absence
**Steps:**
1. Navigate to products page
2. Switch to another tab
3. Wait 30+ minutes
4. Return to products tab

**Expected Result:**
- Products should refresh with latest data
- No authentication issues
- Smooth loading experience

### Scenario 4: Network Disconnection
**Steps:**
1. Navigate to products page
2. Disconnect network (airplane mode or disable WiFi)
3. Switch tabs and return
4. Reconnect network

**Expected Result:**
- Loading state should clear when offline
- Data should refresh when back online
- No stuck loading states

### Scenario 5: Rapid Tab Switching
**Steps:**
1. Navigate to products page
2. Rapidly switch between tabs multiple times
3. Observe loading behavior

**Expected Result:**
- No duplicate API calls
- Loading state should resolve correctly
- No race conditions

### Scenario 6: Loading Timeout
**Steps:**
1. Navigate to products page
2. Simulate slow network (throttle to slow 3G)
3. Wait for 10+ seconds

**Expected Result:**
- Timeout should trigger after 10 seconds
- Automatic retry should occur
- Manual retry button should be available

### Scenario 7: Error Recovery
**Steps:**
1. Navigate to products page
2. Simulate network error (block API calls)
3. Observe error handling

**Expected Result:**
- Error boundary should catch errors
- Retry options should be available
- User-friendly error messages

## Manual Testing Checklist

- [ ] Basic tab switching works correctly
- [ ] Tab switch during loading doesn't cause stuck state
- [ ] Long absence refreshes data properly
- [ ] Network disconnection is handled gracefully
- [ ] Rapid tab switching doesn't cause issues
- [ ] Loading timeout triggers correctly
- [ ] Error boundary catches and handles errors
- [ ] Debug logs are comprehensive and helpful
- [ ] Manual retry button works
- [ ] Automatic retry with backoff works
- [ ] No duplicate API calls
- [ ] Authentication context is maintained
- [ ] Performance is not degraded

## Browser Testing

Test in the following browsers:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

## Performance Considerations

- Monitor for memory leaks from event listeners
- Ensure proper cleanup on component unmount
- Verify no excessive API calls
- Check that timeouts are properly cleared

## Success Criteria

1. **Primary Issue Fixed**: Products table never gets stuck on "Loading products..." when switching tabs
2. **Robust Error Handling**: All error scenarios are handled gracefully
3. **Good User Experience**: Loading states are clear and actionable
4. **Performance**: No degradation in page performance
5. **Reliability**: Works consistently across different browsers and network conditions

## Rollback Plan

If issues are discovered:
1. Revert the ProductsTab component changes
2. Remove the error boundary wrapper
3. Keep only the basic visibility change handling
4. Investigate and fix issues before re-implementing

## Future Improvements

1. Implement similar patterns in other data-loading components
2. Create a custom hook for tab visibility handling
3. Add user preferences for auto-refresh behavior
4. Implement more sophisticated caching strategies
