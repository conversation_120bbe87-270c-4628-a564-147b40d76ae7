import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase'
import { NotificationService } from '@/lib/notification-service'
import { centralizedNotificationService } from '@/lib/centralized-notification-service'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
}

export function useNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()
  const notificationService = new NotificationService()

  useEffect(() => {
    loadNotifications()
    loadUnreadCount()

    // Set up subscription using centralized service
    const unsubscribe = centralizedNotificationService.subscribe(
      (newNotification: any) => {
        // Add new notification to the top of the list only if it doesn't already exist
        setNotifications(prev => {
          // Check if notification already exists
          const exists = prev.some(notification => notification.id === newNotification.id)
          if (!exists) {
            return [newNotification, ...prev]
          }
          return prev
        })
        setUnreadCount(prev => prev + 1)
      },
      (updatedNotification: any) => {
        // Update notification in the list
        setNotifications(prev => 
          prev.map(notification => 
            notification.id === updatedNotification.id 
              ? updatedNotification 
              : notification
          )
        )
        
        // Update unread count if this notification was marked as read
        if (updatedNotification.is_read) {
          setUnreadCount(prev => Math.max(0, prev - 1))
        }
      },
      (deletedNotification: any) => {
        // Remove notification from the list
        setNotifications(prev => 
          prev.filter(notification => notification.id !== deletedNotification.id)
        )
        
        // Update unread count if this notification was unread
        if (!deletedNotification.is_read) {
          setUnreadCount(prev => Math.max(0, prev - 1))
        }
      }
    )

    // Subscribe to custom events
    const unsubscribeCustom = centralizedNotificationService.subscribeToEvent(
      'notifications-marked-as-read',
      () => {
        // Update all notifications to read
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, is_read: true }))
        )
        setUnreadCount(0)
      }
    )

    return () => {
      unsubscribe()
      unsubscribeCustom()
    }
  }, [])

  const loadNotifications = async () => {
    try {
      setLoading(true)
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        const notifications = await notificationService.getNotifications(user.id, 20)
        setNotifications(notifications)
      }
    } catch (error) {
      console.error('Error loading notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadUnreadCount = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        const count = await notificationService.getUnreadNotificationCount(user.id)
        setUnreadCount(count)
      }
    } catch (error) {
      console.error('Error loading unread count:', error)
    }
  }

  const markAsRead = async (id: string) => {
    try {
      // Update database
      await notificationService.markAsRead(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: true } 
            : notification
        )
      )
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const markAllAsRead = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        // Update database
        await notificationService.markAllAsRead(user.id)
        
        // Update local state immediately for instant feedback
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, is_read: true }))
        )
        setUnreadCount(0)
        
        // Broadcast the change to other components
        centralizedNotificationService.broadcastEvent('notifications-marked-as-read', { userId: user.id })
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
    }
  }

  return {
    notifications,
    unreadCount,
    loading,
    markAsRead,
    markAllAsRead
  }
}