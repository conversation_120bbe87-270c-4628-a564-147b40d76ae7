'use client'

import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { EventEmitter } from 'events'
import { toast } from '@/components/ui/use-toast'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
  is_archived: boolean
  user_id: string
}

class CentralizedNotificationService {
  private supabase: ReturnType<typeof createClientComponentClient>
  private eventEmitter = new EventEmitter()
  private channel: any = null
  private isSubscribed = false
  private currentUserId: string | null = null
  private isInitializing = false
  private subscriptionPromise: Promise<void> | null = null
  private subscribers: number = 0
  private unsubscribeCallbacks: (() => void)[] = []

  constructor() {
    // Use createClientComponentClient for proper real-time support
    this.supabase = createClientComponentClient()
    this.initializeAuthListener()
  }

  private async initializeAuthListener() {
    // Get initial session
    const { data: { session } } = await this.supabase.auth.getSession()
    if (session?.user) {
      this.currentUserId = session.user.id
      // Initialize subscription after we have the user ID
      await this.initializeSubscription()
    }

    // Listen for auth changes
    this.supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        this.currentUserId = session.user.id
        // Reinitialize subscription with new user ID
        await this.cleanup()
        await this.initializeSubscription()
      } else if (event === 'SIGNED_OUT') {
        this.currentUserId = null
        await this.cleanup()
      }
    })
  }

  private async initializeSubscription() {
    // Prevent multiple simultaneous initializations
    if (this.isInitializing) {
      return this.subscriptionPromise || Promise.resolve()
    }

    this.isInitializing = true
    this.subscriptionPromise = this.setupSubscription()
    
    try {
      await this.subscriptionPromise
    } finally {
      this.isInitializing = false
      this.subscriptionPromise = null
    }
  }

  private async setupSubscription() {
    try {
      // Clean up any existing subscription
      if (this.channel) {
        try {
          await this.supabase.removeChannel(this.channel)
        } catch (error) {
          console.error('Error removing existing channel:', error)
        }
        this.channel = null
        this.isSubscribed = false
        
        // Add a small delay to avoid connection conflicts
        await new Promise(resolve => setTimeout(resolve, 500))
      }

      if (!this.currentUserId) {
        return
      }

      // Force refresh schema cache before setting up subscription
      await this.refreshSchemaCache();

      this.channel = this.supabase
        .channel(`notifications:${this.currentUserId}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${this.currentUserId}`
          },
          (payload) => {
            // Emit notification for this user
            this.eventEmitter.emit('notification-inserted', payload.new)
            
            // Show toast notification for new notifications
            this.showNotificationToast(payload.new as unknown as Notification)
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${this.currentUserId}`
          },
          (payload) => {
            // Emit notification update
            this.eventEmitter.emit('notification-updated', payload.new)
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'DELETE',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${this.currentUserId}`
          },
          (payload) => {
            // Emit notification deletion
            this.eventEmitter.emit('notification-deleted', payload.old)
          }
        )
        .subscribe((status, err) => {
          if (err) {
            console.error('Subscription error:', err)
          }
          if (status === 'SUBSCRIBED') {
            this.isSubscribed = true
            console.log('Successfully subscribed to notifications')
          } else if (status === 'CHANNEL_ERROR') {
            this.isSubscribed = false
            
            // Try to reconnect after a delay
            setTimeout(() => {
              if (!this.isSubscribed && this.subscribers > 0) {
                this.initializeSubscription()
              }
            }, 5000)
          } else if (status === 'CLOSED') {
            this.isSubscribed = false
            
            // Try to reconnect after a delay if we still have subscribers
            setTimeout(() => {
              if (!this.isSubscribed && this.subscribers > 0) {
                this.initializeSubscription()
              }
            }, 3000)
          }
        })
    } catch (error) {
      console.error('Error setting up subscription:', error)
      this.isInitializing = false
    }
  }

  // Add a method to refresh schema cache
  private async refreshSchemaCache() {
    try {
      // Only log in development mode to reduce console noise
      if (process.env.NODE_ENV === 'development') {
        console.log('Refreshing schema cache for notifications...')
      }
      
      // Query the actual notifications table to force schema refresh
      const { data: notificationData, error: notificationError } = await this.supabase
        .from('notifications')
        .select('id, user_id, title')
        .limit(1)
      
      if (notificationError) {
        console.error('Error in notifications table query:', notificationError)
      } else {
        // Only log in development mode
        if (process.env.NODE_ENV === 'development') {
          console.log('Notifications table query successful')
        }
      }
      
      // Also query products table to ensure schema is refreshed
      const { data: productData, error: productError } = await this.supabase
        .from('products')
        .select('id, user_id, name')
        .limit(1)
      
      if (productError) {
        console.error('Error in products table query:', productError)
      } else {
        // Only log in development mode
        if (process.env.NODE_ENV === 'development') {
          console.log('Products table query successful')
        }
      }
      
      // Only log in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log('Schema cache refresh completed')
      }
    } catch (error) {
      console.error('Error refreshing schema cache:', error)
    }
  }

  // Show toast notification for new notifications
  private showNotificationToast(notification: Notification) {
    // Only show toast for unread notifications
    if (!notification.is_read) {
      toast({
        title: notification.title,
        description: notification.message,
        variant: notification.type === 'warning' ? 'destructive' : 'default'
      })
    }
  }

  // Subscribe to notification events
  subscribe(
    onInsert: (notification: Notification) => void,
    onUpdate: (notification: Notification) => void,
    onDelete: (notification: Notification) => void
  ) {
    this.subscribers++
    
    this.eventEmitter.on('notification-inserted', onInsert)
    this.eventEmitter.on('notification-updated', onUpdate)
    this.eventEmitter.on('notification-deleted', onDelete)

    // If we have a user ID but no active channel, initialize subscription
    if (this.currentUserId && (!this.channel || !this.isSubscribed)) {
      this.initializeSubscription()
    }

    // Return unsubscribe function
    const unsubscribe = () => {
      this.subscribers--
      this.eventEmitter.off('notification-inserted', onInsert)
      this.eventEmitter.off('notification-updated', onUpdate)
      this.eventEmitter.off('notification-deleted', onDelete)
      
      // If no more subscribers, clean up
      if (this.subscribers <= 0) {
        this.cleanup()
      }
    }
    
    this.unsubscribeCallbacks.push(unsubscribe)
    return unsubscribe
  }

  // Cleanup subscription
  async cleanup() {
    if (this.channel) {
      try {
        await this.supabase.removeChannel(this.channel)
        this.channel = null
        this.isSubscribed = false
        
        // Return a promise to ensure we wait for cleanup to complete
        return new Promise<void>(resolve => setTimeout(resolve, 1000))
      } catch (error) {
        console.error('Error cleaning up notification channel:', error)
      }
    }
    return Promise.resolve()
  }

  // Broadcast a custom event to all subscribers
  broadcastEvent(eventType: string, data: any) {
    this.eventEmitter.emit(eventType, data)
  }

  // Subscribe to custom events
  subscribeToEvent(eventType: string, handler: (data: any) => void) {
    this.eventEmitter.on(eventType, handler)
    
    // Return unsubscribe function
    return () => {
      this.eventEmitter.off(eventType, handler)
    }
  }

  // Get current subscription status
  getSubscriptionStatus() {
    return {
      isSubscribed: this.isSubscribed,
      hasChannel: !!this.channel,
      userId: this.currentUserId,
      subscribers: this.subscribers
    }
  }
}

// Export singleton instance
export const centralizedNotificationService = new CentralizedNotificationService()