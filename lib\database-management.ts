import { createSupabaseClient } from './supabase'

/**
 * Database Management Utilities
 * 
 * These functions allow you to manage your Supabase database directly
 * from your Next.js application. Use with caution in production!
 */

interface TableColumn {
  name: string
  type: string
  nullable?: boolean
  defaultValue?: string
  isPrimaryKey?: boolean
  isUnique?: boolean
  references?: {
    table: string
    column: string
    onDelete?: 'CASCADE' | 'SET NULL' | 'RESTRICT'
  }
}

interface CreateTableOptions {
  tableName: string
  columns: TableColumn[]
  enableRLS?: boolean
  policies?: string[]
}

/**
 * Create a new table with columns and constraints
 */
export async function createTable(options: CreateTableOptions) {
  const supabase = createSupabaseClient()
  
  try {
    // Build the CREATE TABLE SQL
    let sql = `CREATE TABLE IF NOT EXISTS ${options.tableName} (\n`
    
    const columnDefinitions = options.columns.map(col => {
      let def = `  ${col.name} ${col.type}`
      
      if (col.isPrimaryKey) def += ' PRIMARY KEY'
      if (!col.nullable && !col.isPrimaryKey) def += ' NOT NULL'
      if (col.defaultValue) def += ` DEFAULT ${col.defaultValue}`
      if (col.isUnique && !col.isPrimaryKey) def += ' UNIQUE'
      if (col.references) {
        def += ` REFERENCES ${col.references.table}(${col.references.column})`
        if (col.references.onDelete) {
          def += ` ON DELETE ${col.references.onDelete}`
        }
      }
      
      return def
    })
    
    sql += columnDefinitions.join(',\n')
    sql += '\n);'
    
    // Execute the CREATE TABLE statement
    const { error } = await supabase.rpc('exec_sql', { sql })
    
    if (error) {
      console.error('Error creating table:', error)
      return { success: false, error }
    }
    
    // Enable RLS if requested
    if (options.enableRLS) {
      const rlsResult = await enableRLS(options.tableName)
      if (!rlsResult.success) {
        return { success: false, error: rlsResult.error }
      }
    }
    
    // Create policies if provided
    if (options.policies && options.policies.length > 0) {
      for (const policy of options.policies) {
        const policyResult = await createPolicy(options.tableName, policy)
        if (!policyResult.success) {
          console.warn(`Failed to create policy for ${options.tableName}:`, policyResult.error)
        }
      }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Error in createTable:', error)
    return { success: false, error }
  }
}

/**
 * Add a new column to an existing table
 */
export async function addColumn(tableName: string, column: TableColumn) {
  const supabase = createSupabaseClient()
  
  try {
    let sql = `ALTER TABLE ${tableName} ADD COLUMN IF NOT EXISTS ${column.name} ${column.type}`
    
    if (!column.nullable) sql += ' NOT NULL'
    if (column.defaultValue) sql += ` DEFAULT ${column.defaultValue}`
    if (column.isUnique) sql += ' UNIQUE'
    if (column.references) {
      sql += ` REFERENCES ${column.references.table}(${column.references.column})`
      if (column.references.onDelete) {
        sql += ` ON DELETE ${column.references.onDelete}`
      }
    }
    
    sql += ';'
    
    const { error } = await supabase.rpc('exec_sql', { sql })
    
    if (error) {
      console.error('Error adding column:', error)
      return { success: false, error }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Error in addColumn:', error)
    return { success: false, error }
  }
}

/**
 * Enable Row Level Security on a table
 */
export async function enableRLS(tableName: string) {
  const supabase = createSupabaseClient()
  
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `ALTER TABLE ${tableName} ENABLE ROW LEVEL SECURITY;`
    })
    
    if (error) {
      console.error('Error enabling RLS:', error)
      return { success: false, error }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Error in enableRLS:', error)
    return { success: false, error }
  }
}

/**
 * Create a Row Level Security policy
 */
export async function createPolicy(tableName: string, policySQL: string) {
  const supabase = createSupabaseClient()
  
  try {
    const { error } = await supabase.rpc('exec_sql', { sql: policySQL })
    
    if (error) {
      console.error('Error creating policy:', error)
      return { success: false, error }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Error in createPolicy:', error)
    return { success: false, error }
  }
}

/**
 * Execute raw SQL (use with extreme caution!)
 */
export async function executeSQL(sql: string) {
  const supabase = createSupabaseClient()
  
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql })
    
    if (error) {
      console.error('Error executing SQL:', error)
      return { success: false, error }
    }
    
    return { success: true, data }
    
  } catch (error) {
    console.error('Error in executeSQL:', error)
    return { success: false, error }
  }
}

/**
 * Create a function in the database
 */
export async function createFunction(functionSQL: string) {
  return executeSQL(functionSQL)
}

/**
 * Create an index on a table
 */
export async function createIndex(tableName: string, columnName: string, indexName?: string) {
  const indexNameFinal = indexName || `idx_${tableName}_${columnName}`
  const sql = `CREATE INDEX IF NOT EXISTS ${indexNameFinal} ON ${tableName}(${columnName});`
  
  return executeSQL(sql)
}

/**
 * Get information about table columns
 */
export async function getTableColumns(tableName: string) {
  const supabase = createSupabaseClient()
  
  try {
    // Query the information_schema to get column information
    const { data, error } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable, column_default')
      .eq('table_name', tableName)
      .eq('table_schema', 'public')
      .order('ordinal_position')
    
    if (error) {
      console.error('Error getting table columns:', error)
      return { success: false, error }
    }
    
    return { success: true, data }
    
  } catch (error) {
    console.error('Error in getTableColumns:', error)
    return { success: false, error }
  }
}

/**
 * Verify if a specific column exists in a table
 */
export async function verifyColumnExists(tableName: string, columnName: string) {
  const supabase = createSupabaseClient()
  
  try {
    // Try to query the actual table with just that column
    try {
      const { data: testData, error: testError } = await supabase
        .from(tableName)
        .select(columnName)
        .limit(1)
      
      if (testError && testError.message.includes('not found')) {
        // Column definitely doesn't exist or schema cache issue
        return { success: true, exists: false, error: testError }
      } else if (testError) {
        // Some other error
        console.error('Error in direct table query:', testError)
        return { success: false, error: testError }
      } else {
        // Query succeeded, column exists
        return { success: true, exists: true, data: testData }
      }
    } catch (directError) {
      console.error('Error in direct table query attempt:', directError)
      return { success: false, error: directError }
    }
  } catch (error) {
    console.error('Error in verifyColumnExists:', error)
    return { success: false, error }
  }
}

/**
 * Drop a table (use with extreme caution!)
 */
export async function dropTable(tableName: string, cascade: boolean = false) {
  const sql = `DROP TABLE IF EXISTS ${tableName}${cascade ? ' CASCADE' : ''};`
  return executeSQL(sql)
}

/**
 * List all tables in the public schema
 */
export async function listAllTables() {
  const supabase = createSupabaseClient();
  
  try {
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name, table_type')
      .eq('table_schema', 'public')
      .order('table_name');
      
    if (error) {
      console.error('Error listing tables:', error);
      return { success: false, error };
    }
    
    return { success: true, data };
  } catch (error) {
    console.error('Error in listAllTables:', error);
    return { success: false, error };
  }
}

/**
 * Get detailed information about a specific table
 */
export async function getTableInfo(tableName: string) {
  const supabase = createSupabaseClient();
  
  try {
    const { data, error } = await supabase
      .from('information_schema.columns')
      .select(`
        column_name,
        data_type,
        is_nullable,
        column_default,
        is_primary_key: constraint_column_usage(constraint_name)
      `)
      .eq('table_name', tableName)
      .eq('table_schema', 'public')
      .order('ordinal_position');
      
    if (error) {
      console.error(`Error getting info for table ${tableName}:`, error);
      return { success: false, error };
    }
    
    return { success: true, data, tableName };
  } catch (error) {
    console.error(`Error in getTableInfo for ${tableName}:`, error);
    return { success: false, error };
  }
}

/**
 * Find all tables with 'product' in their name
 */
export async function findProductTables() {
  const supabase = createSupabaseClient();
  
  try {
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .ilike('table_name', '%product%');
      
    if (error) {
      console.error('Error finding product tables:', error);
      return { success: false, error };
    }
    
    // Get detailed info for each product table
    const tableDetails = [];
    for (const table of data) {
      const tableInfo = await getTableInfo(table.table_name);
      if (tableInfo.success) {
        tableDetails.push({
          tableName: table.table_name,
          columns: tableInfo.data
        });
      }
    }
    
    return { success: true, data: tableDetails };
  } catch (error) {
    console.error('Error in findProductTables:', error);
    return { success: false, error };
  }
}

/**
 * Force refresh the Supabase schema cache
 */
export async function forceRefreshSchema() {
  const supabase = createSupabaseClient()
  
  try {
    console.log('Force refreshing Supabase schema cache...')
    
    // Method 1: Query the actual products table with specific columns
    const { data: productData, error: productError } = await supabase
      .from('products')
      .select('id, user_id, name')
      .limit(1)
    
    if (productError) {
      console.error('Error in products table query:', productError)
    } else {
      console.log('Products table query successful')
    }
    
    // Method 2: Query the notifications table
    const { data: notificationData, error: notificationError } = await supabase
      .from('notifications')
      .select('id, user_id, title')
      .limit(1)
    
    if (notificationError) {
      console.error('Error in notifications table query:', notificationError)
    } else {
      console.log('Notifications table query successful')
    }
    
    // Method 3: Try to query with batch_reference specifically
    try {
      const { data: batchData, error: batchError } = await supabase
        .from('products')
        .select('batch_reference')
        .limit(1)
      
      if (batchError) {
        console.error('Error querying batch_reference:', batchError)
        // This might be expected if the column was recently added
      } else {
        console.log('Batch reference query successful')
      }
    } catch (batchQueryError) {
      console.error('Error in batch reference query attempt:', batchQueryError)
    }
    
    // Method 4: Query stock_movements table to ensure it's in schema cache
    try {
      const { data: stockData, error: stockError } = await supabase
        .from('stock_movements')
        .select('id')
        .limit(1)
      
      if (stockError) {
        console.error('Error querying stock_movements:', stockError)
        // This might be the schema cache issue we're trying to fix
      } else {
        console.log('Stock movements query successful')
      }
    } catch (stockQueryError) {
      console.error('Error in stock movements query attempt:', stockQueryError)
    }
    
    // Method 5: Query product_variants table with cost_adjustment
    try {
      const { data: variantsData, error: variantsError } = await supabase
        .from('product_variants')
        .select('id, product_id, cost_adjustment')
        .limit(1)
      
      if (variantsError) {
        console.error('Error querying product_variants:', variantsError)
        // This might be the schema cache issue we're trying to fix
      } else {
        console.log('Product variants query successful')
      }
    } catch (variantsQueryError) {
      console.error('Error in product variants query attempt:', variantsQueryError)
    }
    
    console.log('Schema refresh completed')
    return { success: true }
    
  } catch (error) {
    console.error('Error in forceRefreshSchema:', error)
    return { success: false, error }
  }
}

// Add a more robust verifyColumnExists function
export async function verifyColumnExistsRobust(tableName: string, columnName: string) {
  const supabase = createSupabaseClient()
  
  try {
    console.log(`Verifying column ${columnName} exists in table ${tableName}...`)
    
    // First, try to query the column directly
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select(columnName)
        .limit(1)
      
      if (error) {
        // If we get a schema cache error, try to refresh and retry
        if (error.message.includes('column') && error.message.includes('not found')) {
          console.log(`Schema cache issue detected for ${tableName}.${columnName}, attempting refresh...`)
          await forceRefreshSchema()
          
          // Retry the query after refresh
          const { data: retryData, error: retryError } = await supabase
            .from(tableName)
            .select(columnName)
            .limit(1)
          
          if (retryError) {
            console.error(`Retry failed for ${tableName}.${columnName}:`, retryError)
            return { success: true, exists: false, error: retryError }
          } else {
            console.log(`Retry successful for ${tableName}.${columnName}`)
            return { success: true, exists: true, data: retryData }
          }
        } else {
          console.error(`Error querying ${tableName}.${columnName}:`, error)
          return { success: false, error }
        }
      } else {
        console.log(`Column ${columnName} exists in table ${tableName}`)
        return { success: true, exists: true, data }
      }
    } catch (directError) {
      console.error(`Direct query error for ${tableName}.${columnName}:`, directError)
      return { success: false, error: directError }
    }
  } catch (error) {
    console.error('Error in verifyColumnExistsRobust:', error)
    return { success: false, error }
  }
}

// Example usage functions for common operations
export const examples = {
  /**
   * Example: Create a simple blog posts table
   */
  createBlogTable: () => createTable({
    tableName: 'blog_posts',
    columns: [
      { name: 'id', type: 'uuid', isPrimaryKey: true, defaultValue: 'gen_random_uuid()' },
      { name: 'user_id', type: 'uuid', nullable: false, references: { table: 'auth.users', column: 'id', onDelete: 'CASCADE' } },
      { name: 'title', type: 'text', nullable: false },
      { name: 'content', type: 'text' },
      { name: 'published', type: 'boolean', defaultValue: 'false' },
      { name: 'created_at', type: 'timestamp with time zone', defaultValue: "timezone('utc'::text, now())" },
      { name: 'updated_at', type: 'timestamp with time zone', defaultValue: "timezone('utc'::text, now())" }
    ],
    enableRLS: true,
    policies: [
      `CREATE POLICY "Users can view own posts" ON blog_posts FOR SELECT USING (auth.uid() = user_id);`,
      `CREATE POLICY "Users can create own posts" ON blog_posts FOR INSERT WITH CHECK (auth.uid() = user_id);`,
      `CREATE POLICY "Users can update own posts" ON blog_posts FOR UPDATE USING (auth.uid() = user_id);`,
      `CREATE POLICY "Users can delete own posts" ON blog_posts FOR DELETE USING (auth.uid() = user_id);`
    ]
  }),
  
  /**
   * Example: Add a new column to products table
   */
  addDiscountColumn: () => addColumn('products', {
    name: 'discount_percentage',
    type: 'decimal(5,2)',
    defaultValue: '0.00'
  }),
  
  /**
   * Example: Create an index for better performance
   */
  createProductsIndex: () => createIndex('products', 'user_id'),
}