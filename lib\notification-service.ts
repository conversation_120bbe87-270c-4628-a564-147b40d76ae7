import { createSupabaseClient } from '@/lib/supabase';

export class NotificationService {
  private supabase = createSupabaseClient();

  // Create a new notification
  async createNotification(data: {
    userId: string;
    type: string;
    title: string;
    message: string;
    relatedEntityType?: string;
    relatedEntityId?: string;
  }) {
    const { error } = await this.supabase
      .from('notifications')
      .insert({
        user_id: data.userId,
        type: data.type,
        title: data.title,
        message: data.message,
        related_entity_type: data.relatedEntityType,
        related_entity_id: data.relatedEntityId,
      });

    if (error) {
      console.error('Error creating notification:', error);
    }
  }

  // Check if user has enabled notifications
  async getUserNotificationPreferences(userId: string) {
    try {
      const { data, error } = await this.supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 means no rows returned
        console.error('Error fetching user preferences:', error);
        // Return default preferences if error
        return {
          email_notifications: true,
          push_notifications: true
        };
      }

      // If no preferences found, return defaults
      if (!data) {
        return {
          email_notifications: true,
          push_notifications: true
        };
      }

      return {
        email_notifications: data.email_notifications ?? true,
        push_notifications: data.push_notifications ?? true
      };
    } catch (error) {
      console.error('Error fetching user preferences:', error);
      // Return default preferences if error
      return {
        email_notifications: true,
        push_notifications: true
      };
    }
  }

  // Get unread notification count for a user
  async getUnreadNotificationCount(userId: string): Promise<number> {
    const { count, error } = await this.supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('is_read', false);

    if (error) {
      console.error('Error fetching notification count:', error);
      return 0;
    }

    return count || 0;
  }

  // Mark notification as read
  async markAsRead(notificationId: string) {
    const { error } = await this.supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', notificationId);

    if (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  // Mark notification as unread
  async markAsUnread(notificationId: string) {
    const { error } = await this.supabase
      .from('notifications')
      .update({ is_read: false })
      .eq('id', notificationId);

    if (error) {
      console.error('Error marking notification as unread:', error);
    }
  }

  // Mark all notifications as read
  async markAllAsRead(userId: string) {
    const { error } = await this.supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('user_id', userId)
      .eq('is_read', false);

    if (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }

  // Get notifications for a user with pagination
  async getNotifications(userId: string, limit: number = 10, offset: number = 0) {
    const { data, error } = await this.supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }

    return data;
  }

  // Get total count of notifications for a user
  async getNotificationCount(userId: string) {
    const { count, error } = await this.supabase
      .from('notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (error) {
      console.error('Error fetching notification count:', error);
      return 0;
    }

    return count || 0;
  }

  // Archive a notification
  async archiveNotification(notificationId: string) {
    const { error } = await this.supabase
      .from('notifications')
      .update({ is_archived: true })
      .eq('id', notificationId);

    if (error) {
      console.error('Error archiving notification:', error);
    }
  }

  // Unarchive a notification
  async unarchiveNotification(notificationId: string) {
    const { error } = await this.supabase
      .from('notifications')
      .update({ is_archived: false })
      .eq('id', notificationId);

    if (error) {
      console.error('Error unarchiving notification:', error);
    }
  }

  // Delete a notification permanently
  async deleteNotification(notificationId: string) {
    const { error } = await this.supabase
      .from('notifications')
      .delete()
      .eq('id', notificationId);

    if (error) {
      console.error('Error deleting notification:', error);
    }
  }
}