import { createSupabaseClient } from '@/lib/supabase';
import { NotificationService } from '@/lib/notification-service';

export class StockNotificationService {
  private supabase = createSupabaseClient();
  private notificationService = new NotificationService();

  /**
   * Check for low stock items and create notifications
   */
  async checkLowStockAndNotify() {
    try {
      const { data: { user } } = await this.supabase.auth.getUser();
      if (!user) {
        console.log('No authenticated user, skipping low stock check');
        return;
      }

      // Get user's notification preferences
      const preferences = await this.notificationService.getUserNotificationPreferences(user.id);
      
      // If user has disabled low stock alerts, skip
      if (!preferences.email_notifications) {
        console.log('Low stock alerts disabled for user, skipping check');
        return;
      }

      // Get all products and variants with low stock
      const { data: products, error: productsError } = await this.supabase
        .from('products')
        .select(`
          id,
          name,
          base_sku,
          stock_quantity,
          low_stock_threshold,
          has_variants,
          product_variants(
            id,
            variant_name,
            sku,
            stock_quantity,
            low_stock_threshold
          )
        `)
        .eq('user_id', user.id)
        .eq('is_active', true);

      if (productsError) {
        console.error('Error fetching products for low stock check:', productsError);
        return;
      }

      // Check for low stock items
      const lowStockItems: Array<{
        type: 'product' | 'variant';
        id: string;
        name: string;
        sku: string;
        currentStock: number;
        threshold: number;
      }> = [];

      products?.forEach(product => {
        // Check simple products
        if (!product.has_variants) {
          const stockQty = product.stock_quantity || 0;
          const threshold = product.low_stock_threshold || 10;
          
          if (stockQty > 0 && stockQty <= threshold) {
            lowStockItems.push({
              type: 'product',
              id: product.id,
              name: product.name,
              sku: product.base_sku || '',
              currentStock: stockQty,
              threshold
            });
          }
        } 
        // Check variants of variable products
        else if (product.product_variants) {
          product.product_variants.forEach(variant => {
            const stockQty = variant.stock_quantity || 0;
            const threshold = variant.low_stock_threshold || 10;
            
            if (stockQty > 0 && stockQty <= threshold) {
              lowStockItems.push({
                type: 'variant',
                id: variant.id,
                name: `${product.name} - ${variant.variant_name || 'Unnamed Variant'}`,
                sku: variant.sku || '',
                currentStock: stockQty,
                threshold
              });
            }
          });
        }
      });

      // Create notifications for low stock items
      for (const item of lowStockItems) {
        const title = `Low Stock Alert: ${item.name}`;
        const message = `Item "${item.name}" (SKU: ${item.sku}) is running low. Current stock: ${item.currentStock}, Threshold: ${item.threshold}`;
        
        await this.notificationService.createNotification({
          userId: user.id,
          type: 'low_stock',
          title,
          message,
          relatedEntityType: item.type,
          relatedEntityId: item.id
        });
      }

      console.log(`Created ${lowStockItems.length} low stock notifications`);
    } catch (error) {
      console.error('Error in checkLowStockAndNotify:', error);
    }
  }

  /**
   * Create notification for stock adjustment
   * NOTE: This method is deprecated as notifications are now created via database triggers
   * This is kept for reference and backward compatibility, but should not be used for new code
   */
  async createStockAdjustmentNotification(
    userId: string,
    productName: string,
    sku: string,
    previousQuantity: number,
    newQuantity: number,
    reason: string
  ) {
    console.warn('createStockAdjustmentNotification is deprecated. Using database triggers instead.');
    // No longer creating notifications at application level
    // Stock adjustment notifications are now handled by database triggers
  }
}