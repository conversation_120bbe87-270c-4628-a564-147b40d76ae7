import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

// Define the type for our client instance
type SupabaseClientType = ReturnType<typeof createClientComponentClient<Database>>

// DEPRECATED: Use getSupabaseClient() instead to ensure proper authentication context
export const createSupabaseClient = () => {
  return createClientComponentClient<Database>()
}

// Export a function to get a client rather than a singleton instance
// This ensures we always get the current session
export const getSupabaseClient = () => {
  return createClientComponentClient<Database>()
}

// Product Attributes Functions
export async function getProductAttributes(userId: string) {
  const supabase = getSupabaseClient()
  
  try {
    const { data, error } = await supabase
      .from('product_attributes')
      .select('*')
      .eq('user_id', userId)
      .order('name')
      
    if (error) {
      console.error('Error fetching product attributes:', error)
      throw error
    }
    
    return data
  } catch (error) {
    console.error('Error in getProductAttributes:', error)
    throw error
  }
}

export async function createProductAttribute(userId: string, name: string) {
  const supabase = getSupabaseClient()
  
  try {
    const { data, error } = await supabase
      .from('product_attributes')
      .insert({ user_id: userId, name })
      .select()
      .single()
      
    if (error) {
      console.error('Error creating product attribute:', error)
      throw error
    }
    
    return data
  } catch (error) {
    console.error('Error in createProductAttribute:', error)
    throw error
  }
}

// New functions for handling attribute values
export async function getProductAttributeValues(userId: string, attributeId: string) {
  const supabase = getSupabaseClient()
  
  try {
    const { data, error } = await supabase
      .from('product_attribute_values')
      .select('value')
      .eq('user_id', userId)
      .eq('attribute_id', attributeId)
      .order('value')
      
    if (error) {
      console.error('Error fetching product attribute values:', error)
      throw error
    }
    
    return data.map(item => ({ value: item.value, label: item.value }))
  } catch (error) {
    console.error('Error in getProductAttributeValues:', error)
    throw error
  }
}

export async function createProductAttributeValue(userId: string, attributeId: string, value: string) {
  const supabase = getSupabaseClient()
  
  try {
    const { data, error } = await supabase
      .from('product_attribute_values')
      .insert({ user_id: userId, attribute_id: attributeId, value })
      .select()
      .single()
      
    if (error) {
      // If it's a duplicate value error, we can ignore it
      if (error.code === '23505') { // Unique violation
        // Only log in development mode
        if (process.env.NODE_ENV === 'development') {
          console.log('Attribute value already exists:', value)
        }
        return null
      }
      console.error('Error creating product attribute value:', error)
      throw error
    }
    
    return data
  } catch (error) {
    console.error('Error in createProductAttributeValue:', error)
    throw error
  }
}

// Function to get all products for a user with their variants and categories (including drafts)
export async function getAllProductsForUser(userId: string) {
  const supabase = getSupabaseClient()
  
  try {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories(
          name
        ),
        product_variants(*)
      `)
      .eq('user_id', userId)
      
    if (error) {
      console.error('Error fetching all products for user:', error)
      throw error
    }
    
    return data
  } catch (error) {
    console.error('Error in getAllProductsForUser:', error)
    throw error
  }
}

// Function to get all products for a user with their variants and categories (only active products)
export async function getProductsForUser(userId: string) {
  const supabase = getSupabaseClient()
  
  try {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories(
          name
        ),
        product_variants(*)
      `)
      .eq('user_id', userId)
      .eq('is_active', true)  // Only fetch active products for inventory
      
    if (error) {
      console.error('Error fetching products for user:', error)
      throw error
    }
    
    return data
  } catch (error) {
    console.error('Error in getProductsForUser:', error)
    throw error
  }
}

// Function to verify and refresh the schema cache
export async function verifyProductSchema() {
  const supabase = createSupabaseClient();
  try {
    // Attempt to query with batch_reference to verify column exists
    const { data, error } = await supabase
      .from('products')
      .select('batch_reference')
      .limit(1);
      
    if (error && error.message.includes('column') && error.message.includes('batch_reference')) {
      return { valid: false, error: 'batch_reference column not found in schema' };
    }
    
    // Verify product_variants schema as well
    const { data: variantData, error: variantError } = await supabase
      .from('product_variants')
      .select('cost_adjustment')
      .limit(1);
      
    if (variantError && variantError.message.includes('column')) {
      return { valid: false, error: 'Schema issue with product_variants table: ' + variantError.message };
    }
    
    return { valid: true, error: null };
  } catch (error: unknown) {
    if (error instanceof Error) {
      // Check if it's a schema cache error
      if (error.message.includes('column') && error.message.includes('not found')) {
        return { valid: false, error: 'Schema cache issue detected' };
      }
      return { valid: false, error: error.message };
    }
    return { valid: false, error: 'Unknown error occurred' };
  }
}

// Function to refresh the Supabase schema cache
export async function refreshSchemaCache() {
  const supabase = createSupabaseClient();
  try {
    // Multiple approaches to refresh schema cache
    console.log('Refreshing schema cache...');
    
    // Approach 1: Query products table with basic columns
    console.log('Approach 1: Querying products table with basic columns...');
    const { data, error } = await supabase
      .from('products')
      .select('id, user_id, name')
      .limit(1);
      
    if (error) {
      console.error('Error refreshing schema cache (attempt 1):', error);
    } else {
      console.log('First products query successful');
    }
    
    // Approach 2: Query notifications table
    console.log('Approach 2: Querying notifications table...');
    const { data: notificationData, error: notificationError } = await supabase
      .from('notifications')
      .select('id, user_id, title')
      .limit(1);
      
    if (notificationError) {
      console.error('Error in notifications query:', notificationError);
    } else {
      console.log('Notifications query successful');
    }
    
    // Approach 3: Query product_variants table with necessary fields
    console.log('Approach 3: Querying product_variants table with cost_adjustment...');
    const { data: variantsData, error: variantsError } = await supabase
      .from('product_variants')
      .select('id, product_id, cost_adjustment, price')
      .limit(1);
      
    if (variantsError) {
      console.error('Error in product_variants query:', variantsError);
    } else {
      console.log('Product variants query successful');
    }
    
    // Approach 4: Try to query with batch_reference specifically
    console.log('Approach 4: Testing batch_reference column...');
    try {
      const { data: batchData, error: batchError } = await supabase
        .from('products')
        .select('batch_reference')
        .limit(1);
      
      if (batchError) {
        console.error('Error querying batch_reference:', batchError);
        if (batchError.message.includes('batch_reference')) {
          console.log('This is expected if the schema cache needs time to refresh');
        }
      } else {
        console.log('Batch reference query successful');
      }
    } catch (batchQueryError) {
      console.error('Error in batch reference query attempt:', batchQueryError);
    }
    
    console.log('Schema cache refresh attempt completed');
    return { success: true, error: null };
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error refreshing schema cache:', error);
      return { success: false, error: error.message };
    }
    console.error('Unknown error refreshing schema cache');
    return { success: false, error: 'Unknown error occurred' };
  }
}

// Custom function to execute SQL (requires proper setup in Supabase)
export async function execSQL(sql: string) {
  const supabase = createSupabaseClient()
  
  try {
    // Note: This requires a custom RPC function in Supabase
    // For now, this is commented out as it requires backend setup
    throw new Error('Custom SQL execution not implemented')
    // const { data, error } = await supabase.rpc('exec_sql', { sql })
    // return { data, error }
  } catch (err) {
    return { data: null, error: err }
  }
}

// Helper types for better TypeScript support
export type ProductRow = Database['public']['Tables']['products']['Row']
export type ProductInsert = Database['public']['Tables']['products']['Insert']
export type ProductUpdate = Database['public']['Tables']['products']['Update']

export type ProductVariantRow = Database['public']['Tables']['product_variants']['Row']
export type ProductVariantInsert = Database['public']['Tables']['product_variants']['Insert']
export type ProductVariantUpdate = Database['public']['Tables']['product_variants']['Update']

export type StockMovementRow = Database['public']['Tables']['stock_movements']['Row']
export type StockMovementInsert = Database['public']['Tables']['stock_movements']['Insert']
export type StockMovementUpdate = Database['public']['Tables']['stock_movements']['Update']

export type ExpenseRow = Database['public']['Tables']['expenses']['Row']
export type ExpenseInsert = Database['public']['Tables']['expenses']['Insert']
export type ExpenseUpdate = Database['public']['Tables']['expenses']['Update']

export type CategoryRow = Database['public']['Tables']['categories']['Row']
export type CategoryInsert = Database['public']['Tables']['categories']['Insert']
export type CategoryUpdate = Database['public']['Tables']['categories']['Update']

export type ProfileRow = Database['public']['Tables']['profiles']['Row']
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert']
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update']

// Product with variants type (for complex queries)
export type ProductWithVariants = ProductRow & {
  variants?: ProductVariantRow[]
}

// Database types (will be generated from Supabase)
export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          business_name: string | null
          contact_info: any | null
          subscription_tier: string
          currency: string
          avatar_url: string | null
          first_name: string | null
          last_name: string | null
          display_name: string | null
          job_title: string | null
          created_at: string
        }
        Insert: {
          id: string
          business_name?: string | null
          contact_info?: any | null
          subscription_tier?: string
          currency?: string
          avatar_url?: string | null
          first_name?: string | null
          last_name?: string | null
          display_name?: string | null
          job_title?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          business_name?: string | null
          contact_info?: any | null
          subscription_tier?: string
          currency?: string
          avatar_url?: string | null
          first_name?: string | null
          last_name?: string | null
          display_name?: string | null
          job_title?: string | null
          created_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          description?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          description?: string | null
          created_at?: string
        }
      }
      products: {
        Row: {
          id: string
          user_id: string
          category_id: string | null
          name: string
          description: string | null
          brand: string | null
          supplier: string | null
          base_sku: string | null
          has_variants: boolean
          track_inventory: boolean
          is_active: boolean
          base_cost: number | null
          packaging_cost: number
          price: number | null
          size: string | null
          color: string | null
          stock_quantity: number
          low_stock_threshold: number
          barcode: string | null
          image_url: string | null
          sale_price: number | null
          sale_start_date: string | null
          sale_end_date: string | null
          batch_reference: string | null
          purchase_date: string | null
          notes: string | null
          total_cost: number | null
          effective_price: number | null
          is_on_sale: boolean | null
          profit_amount: number | null
          profit_margin: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          category_id?: string | null
          name: string
          description?: string | null
          brand?: string | null
          supplier?: string | null
          base_sku?: string | null
          has_variants?: boolean
          track_inventory?: boolean
          is_active?: boolean
          base_cost?: number | null
          packaging_cost?: number
          price?: number | null
          size?: string | null
          color?: string | null
          stock_quantity?: number
          low_stock_threshold?: number
          barcode?: string | null
          image_url?: string | null
          sale_price?: number | null
          sale_start_date?: string | null
          sale_end_date?: string | null
          batch_reference?: string | null
          purchase_date?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          category_id?: string | null
          name?: string
          description?: string | null
          brand?: string | null
          supplier?: string | null
          base_sku?: string | null
          has_variants?: boolean
          track_inventory?: boolean
          is_active?: boolean
          base_cost?: number | null
          packaging_cost?: number
          price?: number | null
          size?: string | null
          color?: string | null
          stock_quantity?: number
          low_stock_threshold?: number
          barcode?: string | null
          image_url?: string | null
          sale_price?: number | null
          sale_start_date?: string | null
          sale_end_date?: string | null
          batch_reference?: string | null
          purchase_date?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      product_variants: {
        Row: {
          id: string
          product_id: string
          user_id: string
          sku: string
          variant_name: string | null
          size: string | null
          color: string | null
          material: string | null
          style: string | null
          weight: number | null
          dimensions: any | null
          cost_adjustment: number
          price: number
          sale_price: number | null
          sale_start_date: string | null
          sale_end_date: string | null
          stock_quantity: number
          low_stock_threshold: number
          reserved_quantity: number
          is_active: boolean
          barcode: string | null
          image_urls: string[] | null
          total_cost: number | null
          effective_price: number | null
          is_on_sale: boolean | null
          available_quantity: number | null
          profit_amount: number | null
          profit_margin: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          product_id: string
          user_id: string
          sku: string
          variant_name?: string | null
          size?: string | null
          color?: string | null
          material?: string | null
          style?: string | null
          weight?: number | null
          dimensions?: any | null
          cost_adjustment?: number
          price: number
          sale_price?: number | null
          sale_start_date?: string | null
          sale_end_date?: string | null
          stock_quantity?: number
          low_stock_threshold?: number
          reserved_quantity?: number
          is_active?: boolean
          barcode?: string | null
          image_urls?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          product_id?: string
          user_id?: string
          sku?: string
          variant_name?: string | null
          size?: string | null
          color?: string | null
          material?: string | null
          style?: string | null
          weight?: number | null
          dimensions?: any | null
          cost_adjustment?: number
          price?: number
          sale_price?: number | null
          sale_start_date?: string | null
          sale_end_date?: string | null
          stock_quantity?: number
          low_stock_threshold?: number
          reserved_quantity?: number
          is_active?: boolean
          barcode?: string | null
          image_urls?: string[] | null
          created_at?: string
          updated_at?: string
        }
      }
      stock_movements: {
        Row: {
          id: string
          user_id: string
          product_id: string | null
          variant_id: string | null
          movement_type: 'initial_stock' | 'purchase' | 'sale' | 'adjustment' | 'transfer' | 'damage' | 'return' | 'reserve' | 'unreserve'
          quantity_change: number
          previous_quantity: number
          new_quantity: number
          reason: string | null
          reference_id: string | null
          unit_cost: number | null
          notes: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          product_id?: string | null
          variant_id?: string | null
          movement_type: 'initial_stock' | 'purchase' | 'sale' | 'adjustment' | 'transfer' | 'damage' | 'return' | 'reserve' | 'unreserve'
          quantity_change: number
          previous_quantity: number
          new_quantity: number
          reason?: string | null
          reference_id?: string | null
          unit_cost?: number | null
          notes?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          product_id?: string | null
          variant_id?: string | null
          movement_type?: 'initial_stock' | 'purchase' | 'sale' | 'adjustment' | 'transfer' | 'damage' | 'return' | 'reserve' | 'unreserve'
          quantity_change?: number
          previous_quantity?: number
          new_quantity?: number
          reason?: string | null
          reference_id?: string | null
          unit_cost?: number | null
          notes?: string | null
          created_at?: string
        }
      }
      expenses: {
        Row: {
          id: string
          user_id: string
          expense_id: string | null
          category: string
          amount: number
          description: string | null
          vendor: string | null
          payment_method: string | null
          receipt_url: string | null
          expense_date: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          expense_id?: string | null
          category: string
          amount: number
          description?: string | null
          vendor?: string | null
          payment_method?: string | null
          receipt_url?: string | null
          expense_date: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          expense_id?: string | null
          category?: string
          amount?: number
          description?: string | null
          vendor?: string | null
          payment_method?: string | null
          receipt_url?: string | null
          expense_date?: string
          created_at?: string
        }
      }
      product_attributes: {
        Row: {
          id: string
          user_id: string
          name: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}