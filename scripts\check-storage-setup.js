// Script to check if the avatars storage bucket is properly configured
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in .env.local');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkStorageSetup() {
  console.log('🔍 Checking avatars storage bucket configuration...');
  
  try {
    // List all buckets to see if avatars bucket exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      throw new Error(`Error listing buckets: ${listError.message}`);
    }
    
    const avatarsBucket = buckets.find(bucket => bucket.name === 'avatars');
    
    if (!avatarsBucket) {
      console.log('❌ Avatars bucket not found');
      console.log('\n💡 To fix this:');
      console.log('1. Run: node scripts/fix-avatars-bucket.js');
      console.log('2. Or create the bucket manually through the Supabase dashboard');
      return;
    }
    
    console.log('✅ Avatars bucket exists');
    console.log(`   Name: ${avatarsBucket.name}`);
    console.log(`   Public: ${avatarsBucket.public}`);
    console.log(`   File size limit: ${avatarsBucket.file_size_limit || 'None'}`);
    console.log(`   Allowed MIME types: ${avatarsBucket.allowed_mime_types?.join(', ') || 'All'}`);
    
    // Check if bucket has correct settings
    const issues = [];
    
    if (!avatarsBucket.public) {
      issues.push('Bucket is not public');
    }
    
    if (avatarsBucket.file_size_limit !== 5242880) { // 5MB in bytes
      issues.push('File size limit is not set to 5MB');
    }
    
    const requiredMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    const currentMimeTypes = avatarsBucket.allowed_mime_types || [];
    const missingMimeTypes = requiredMimeTypes.filter(type => !currentMimeTypes.includes(type));
    
    if (missingMimeTypes.length > 0) {
      issues.push(`Missing MIME types: ${missingMimeTypes.join(', ')}`);
    }
    
    if (issues.length > 0) {
      console.log('⚠️  Bucket configuration issues found:');
      issues.forEach(issue => console.log(`   - ${issue}`));
      console.log('\n💡 Run node scripts/fix-avatars-bucket.js to fix these issues');
    } else {
      console.log('✅ Avatars bucket is properly configured');
    }
    
    console.log('\n📋 Next steps:');
    console.log('1. Set up storage policies through the Supabase dashboard');
    console.log('2. Follow instructions in STORAGE_SETUP_INSTRUCTIONS.md');
    console.log('3. Test profile picture upload in the application');
    
  } catch (error) {
    console.error('❌ Error checking storage setup:', error.message);
    console.error('\n💡 Make sure your Supabase credentials are correctly set in .env.local');
  }
}

checkStorageSetup();