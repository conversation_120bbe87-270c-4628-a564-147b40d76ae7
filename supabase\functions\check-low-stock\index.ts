// Supabase Edge Function to check for low stock items and create notifications
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

console.log("Starting low stock check function");

serve(async (_req) => {
  try {
    // Create a Supabase client with the service role key for full access
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    console.log("Checking for low stock items...");

    // Get all active products with low stock
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select(`
        id,
        user_id,
        name,
        base_sku,
        stock_quantity,
        low_stock_threshold,
        has_variants,
        product_variants(
          id,
          variant_name,
          sku,
          stock_quantity,
          low_stock_threshold
        )
      `)
      .eq('is_active', true);

    if (productsError) {
      console.error('Error fetching products:', productsError);
      return new Response(JSON.stringify({ error: productsError.message }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    }

    let notificationCount = 0;

    // Check each product
    for (const product of products || []) {
      // Check simple products
      if (!product.has_variants) {
        const stockQty = product.stock_quantity || 0;
        const threshold = product.low_stock_threshold || 10;
        
        if (stockQty > 0 && stockQty <= threshold) {
          const title = `Low Stock Alert: ${product.name}`;
          const message = `Item "${product.name}" (SKU: ${product.base_sku || 'N/A'}) is running low. Current stock: ${stockQty}, Threshold: ${threshold}`;
          
          // Create notification
          const { error: notificationError } = await supabase
            .from('notifications')
            .insert({
              user_id: product.user_id,
              type: 'low_stock',
              title: title,
              message: message,
              related_entity_type: 'product',
              related_entity_id: product.id,
              created_at: new Date().toISOString()
            });
          
          if (notificationError) {
            console.error('Error creating notification:', notificationError);
          } else {
            notificationCount++;
          }
        }
      } 
      // Check variants of variable products
      else if (product.product_variants) {
        for (const variant of product.product_variants) {
          const stockQty = variant.stock_quantity || 0;
          const threshold = variant.low_stock_threshold || 10;
          
          if (stockQty > 0 && stockQty <= threshold) {
            const title = `Low Stock Alert: ${product.name} - ${variant.variant_name || 'Unnamed Variant'}`;
            const message = `Item "${product.name} - ${variant.variant_name || 'Unnamed Variant'}" (SKU: ${variant.sku || 'N/A'}) is running low. Current stock: ${stockQty}, Threshold: ${threshold}`;
            
            // Create notification
            const { error: notificationError } = await supabase
              .from('notifications')
              .insert({
                user_id: product.user_id,
                type: 'low_stock',
                title: title,
                message: message,
                related_entity_type: 'variant',
                related_entity_id: variant.id,
                created_at: new Date().toISOString()
              });
            
            if (notificationError) {
              console.error('Error creating notification:', notificationError);
            } else {
              notificationCount++;
            }
          }
        }
      }
    }

    console.log(`Created ${notificationCount} low stock notifications`);
    
    return new Response(JSON.stringify({ 
      message: `Successfully checked low stock items. Created ${notificationCount} notifications.` 
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" }
    });
  } catch (error) {
    console.error('Error in low stock check function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { "Content-Type": "application/json" }
    });
  }
});