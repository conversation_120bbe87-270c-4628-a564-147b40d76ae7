// Supabase Edge Function to create notifications for stock adjustments
// WARNING: This function is deprecated as notifications are now created via database triggers
// This file is kept for reference but should not be used
// See COMPLETE_NOTIFICATION_TRIGGERS_SQL.sql for the database trigger implementation

import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

console.log("Stock adjustment notification function loaded (DEPRECATED)";

serve(async (req) => {
  return new Response(JSON.stringify({ 
    status: "deprecated", 
    message: "This function is deprecated. Notifications are now created via database triggers."
  }), {
    status: 200,
    headers: { "Content-Type": "application/json" }
  });
});